#!/usr/bin/env python3
"""
4-Model Comparison Training Script
Runs 4 models: UNet, ResUNet, TransUNet, CSWin-UNet
"""

import os
import sys
import time
import json
import subprocess
import argparse
from datetime import datetime

def clean_old_results(dataset_type):
    """Clean old results and logs for specific dataset"""
    print("🧹 Cleaning old results and logs...")

    # Remove only the result directory for current dataset
    result_dir = f"Result_{dataset_type.upper()}_All"
    if os.path.exists(result_dir):
        import shutil
        try:
            shutil.rmtree(result_dir)
            print(f"  Removed directory: {result_dir}")
        except PermissionError as e:
            print(f"  ⚠️ Warning: Could not remove {result_dir} - {e}")
            print(f"  Continuing anyway...")
        except Exception as e:
            print(f"  ⚠️ Warning: Error removing {result_dir} - {e}")
            print(f"  Continuing anyway...")

    print("✅ Cleanup completed!")

def setup_result_directory(dataset_type):
    """Setup organized result directory structure"""
    result_dir = f"Result_{dataset_type.upper()}_All"
    
    print(f"📁 Setting up result directory: {result_dir}")
    
    # Create main result directory
    os.makedirs(result_dir, exist_ok=True)
    
    # Create subdirectories for each model
    models = ['unet', 'resunet', 'transunet', 'cswinunet']
    
    for model in models:
        # Create model directory
        model_dir = os.path.join(result_dir, model)
        os.makedirs(model_dir, exist_ok=True)
        
        # Create subdirectories
        subdirs = ['checkpoints', 'logs', 'plots', 'predictions']
        for subdir in subdirs:
            os.makedirs(os.path.join(model_dir, subdir), exist_ok=True)
    
    print(f"✅ Result directory structure created: {result_dir}")
    return result_dir

def run_single_model(model_name, dataset_type, result_dir, epochs=100):
    """Run a single model with organized output structure"""
    
    print(f"\n{'='*80}")
    print(f"🚀 Starting {model_name.upper()} training for {dataset_type.upper()} dataset")
    print(f"Epochs: {epochs}")
    print(f"Result directory: {result_dir}/{model_name}")
    print(f"{'='*80}")
    
    # Define trainer scripts
    trainer_scripts = {
        'unet': 'training_scripts/pytorch_unet_trainer.py',
        'resunet': 'training_scripts/pytorch_resunet_trainer.py',
        'transunet': 'training_scripts/pytorch_transunet_trainer.py',
        'cswinunet': 'training_scripts/pytorch_cswinunet_trainer.py'
    }
    
    if model_name not in trainer_scripts:
        print(f"❌ Unknown model: {model_name}")
        return False
    
    script_path = trainer_scripts[model_name]
    
    if not os.path.exists(script_path):
        print(f"❌ Training script not found: {script_path}")
        return False
    
    # Create model-specific working directory
    model_dir = os.path.join(result_dir, model_name)
    
    try:
        # Create command
        cmd = [
            sys.executable, script_path,
            '--data_dir', './data-if',
            '--dataset_type', dataset_type,
            '--epochs', str(epochs),
            '--batch_size', '8',
            '--learning_rate', '0.001',
            '--output_dir', model_dir
        ]
        
        start_time = time.time()
        print(f"⏰ Started at: {datetime.now().strftime('%H:%M:%S')}")
        
        # Run training
        result = subprocess.run(cmd, timeout=14400)  # 4 hours timeout
        
        training_time = time.time() - start_time
        success = result.returncode == 0
        
        if success:
            print(f"\n✅ {model_name.upper()} {dataset_type.upper()} COMPLETED!")
            print(f"⏰ Training time: {training_time/3600:.2f} hours")
        else:
            print(f"\n❌ {model_name.upper()} {dataset_type.upper()} FAILED!")
            print(f"Return code: {result.returncode}")
        print(f"{'='*80}")
        
        return success
        
    except subprocess.TimeoutExpired:
        training_time = time.time() - start_time
        print(f"\n⏰ {model_name.upper()} {dataset_type.upper()} - Timeout after 4 hours")
        return False
    except Exception as e:
        print(f"\n❌ {model_name.upper()} {dataset_type.upper()} - Error: {str(e)}")
        return False

def clean_gpu_memory():
    """Clean GPU memory between models"""
    try:
        import torch
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            print("🧹 GPU memory cleaned")
    except ImportError:
        pass

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='4-Model Comparison Training')
    parser.add_argument('dataset_type', choices=['ir', 'rgb'], 
                       help='Dataset type (ir or rgb)')
    parser.add_argument('--epochs', type=int, default=100,
                       help='Number of epochs (default: 100)')
    
    args = parser.parse_args()
    
    print("🎯 4-MODEL COMPARISON TRAINING")
    print("="*80)
    print(f"Dataset: {args.dataset_type.upper()}")
    print(f"Epochs: {args.epochs}")
    print("Models: UNet, ResUNet, TransUNet, CSWin-UNet")
    print("="*80)
    
    # Clean old results
    clean_old_results(args.dataset_type)
    
    # Setup result directory
    result_dir = setup_result_directory(args.dataset_type)
    
    # Define models
    models = ['unet', 'resunet', 'transunet', 'cswinunet']
    
    print(f"\n🚀 Starting training of {len(models)} models...")
    print(f"📁 Results will be saved in: {result_dir}")
    print("🔄 Running models sequentially...")
    
    start_time = time.time()
    results = {}
    
    for i, model in enumerate(models, 1):
        print(f"\n📊 Progress: {i}/{len(models)} - {model.upper()}")
        success = run_single_model(model, args.dataset_type, result_dir, args.epochs)
        results[model] = success
        
        # Clean GPU memory between models
        clean_gpu_memory()
        
        # Small delay between models
        if i < len(models):
            print("⏳ Waiting 30 seconds before next model...")
            time.sleep(30)
    
    total_time = time.time() - start_time
    
    # Final summary
    print("\n" + "="*80)
    print("🏁 4-MODEL COMPARISON COMPLETED")
    print("="*80)
    print(f"⏰ Total time: {total_time/3600:.2f} hours")
    
    successful = sum(1 for success in results.values() if success)
    print(f"📊 Results: {successful}/{len(models)} models completed successfully")
    
    print("\n📋 Individual Results:")
    for model, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {status} {model.upper()}")
    
    if successful > 0:
        print(f"\n📁 All results organized in: {result_dir}")
        print(f"📊 Each model has:")
        print(f"  - logs/ : Training logs and metrics")
        print(f"  - checkpoints/ : Best model weights")
        print(f"  - plots/ : Training curves")
        print(f"  - predictions/ : Model predictions")
    
    print("="*80)

if __name__ == "__main__":
    main()
