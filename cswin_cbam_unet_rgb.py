#!/usr/bin/env python3
"""
CSWin-CBAM-UNet: AGGRESSIVE OPTIMIZATION FOR VALIDATION IoU > 0.7
Target: Validation IoU > 0.7 during training process

AGGRESSIVE Optimizations:
- Higher learning rate (max_lr=0.005) with OneCycleLR
- Extended training (150 epochs) for better convergence
- Enhanced loss function with class weighting
- Improved model architecture with better feature fusion
- Optimized data augmentation balance
- Advanced training strategies
- Focus on VALIDATION performance during training
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
from torch.amp import GradScaler, autocast
from torch.optim.lr_scheduler import OneCycleLR
import numpy as np
import cv2
from PIL import Image
import glob
import logging
from tqdm import tqdm
import matplotlib.pyplot as plt
from torchvision import transforms
import math
import random
import gc
import time
# import albumentations as A
# from albumentations.pytorch import ToTensorV2
import warnings

warnings.filterwarnings('ignore')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# GPU Performance Optimization
def optimize_gpu_performance():
    """Optimize GPU performance for maximum throughput"""
    if torch.cuda.is_available():
        # Enable TensorFloat-32 (TF32) for faster training on Ampere GPUs
        torch.backends.cuda.matmul.allow_tf32 = True
        torch.backends.cudnn.allow_tf32 = True

        # Enable cuDNN benchmark for consistent input sizes
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False

        # Set memory fraction to avoid fragmentation
        torch.cuda.empty_cache()
        gc.collect()

        logger.info("🚀 GPU Performance Optimizations Enabled:")
        logger.info("   ✅ TF32 enabled for faster training")
        logger.info("   ✅ cuDNN benchmark enabled")
        logger.info("   ✅ Memory optimizations applied")


class AdaptiveLRScheduler:
    """ADAPTIVE learning rate scheduler with IoU-based restarts - OPTIMIZED FOR RGB"""

    def __init__(self, optimizer, total_epochs=150, max_lr=0.003,
                 dataset_type='rgb'):  # OPTIMIZED: Higher max_lr for RGB
        self.optimizer = optimizer
        self.total_epochs = total_epochs
        self.dataset_type = dataset_type

        # RGB-specific learning rate optimization - ULTRA STABLE VERSION
        if dataset_type == 'rgb':
            self.max_lr = max(max_lr, 0.0015)  # Ultra stable max LR for RGB
            self.warmup_epochs = 15  # Extended warmup for RGB stability
            self.eta_min = self.max_lr / 200  # Very conservative minimum LR for RGB
        else:
            self.max_lr = max_lr
            self.warmup_epochs = 5
            self.eta_min = max_lr / 100

        self.current_epoch = 0
        self.best_iou = 0.0
        self.plateau_counter = 0
        self.restart_count = 0

        # RGB-optimized CosineAnnealingLR with warm restarts
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=total_epochs // 3,  # First restart at 1/3 of training
            T_mult=2,  # Double the period after each restart
            eta_min=self.eta_min
        )

        logger.info(f"🔥 RGB-OPTIMIZED LR Scheduler for {total_epochs} epochs")
        logger.info(f"   Max LR: {self.max_lr} (OPTIMIZED for {dataset_type.upper()})")
        logger.info(f"   Warmup epochs: {self.warmup_epochs}")
        logger.info(f"   Strategy: CosineAnnealingWarmRestarts with RGB optimization")

    def step(self, epoch=None, current_iou=None):
        """Step with adaptive restart based on IoU plateau"""
        self.current_epoch = epoch if epoch is not None else self.current_epoch + 1

        # FIXED: More aggressive IoU-based LR adjustment
        if current_iou is not None:
            if current_iou > self.best_iou + 0.005:  # Lower threshold for improvement
                self.best_iou = current_iou
                self.plateau_counter = 0
            else:
                self.plateau_counter += 1

            # FIXED: Earlier and more frequent LR reduction
            if self.plateau_counter >= 8:  # Shorter patience
                self.reduce_lr()
                self.plateau_counter = 0

        self.scheduler.step()
        return self.get_current_lr()

    def reduce_lr(self):
        """FIXED: Reduce learning rate when plateau"""
        current_lr = self.get_current_lr()
        new_lr = current_lr * 0.5  # Reduce by half
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = max(new_lr, self.max_lr / 1000)  # Don't go below min
        logger.info(f"🔥 LR reduced: {current_lr:.6f} → {new_lr:.6f}")

    def restart_lr(self, current_iou):
        """Restart learning rate when IoU plateaus"""
        # Adaptive restart LR based on current IoU level
        if current_iou > 0.6:
            restart_lr = 0.002  # Lower restart for high IoU
        else:
            restart_lr = 0.003  # Higher restart for medium IoU

        for param_group in self.optimizer.param_groups:
            param_group['lr'] = restart_lr

        logger.info(f"🔄 LR RESTART #{self.restart_count + 1}: IoU plateau at {current_iou:.4f}")
        logger.info(f"   New LR: {restart_lr}")

    def get_current_lr(self):
        """Get current learning rate"""
        return self.optimizer.param_groups[0]['lr']


class HyperparameterOptimizer:
    """Automatic hyperparameter optimization during training"""

    def __init__(self):
        self.best_iou = 0.0
        self.best_params = {}
        self.trial_history = []

    def suggest_batch_size(self, gpu_memory_gb):
        """Suggest optimal batch size based on GPU memory"""
        if gpu_memory_gb >= 24:
            return 8
        elif gpu_memory_gb >= 16:
            return 6
        elif gpu_memory_gb >= 12:
            return 4
        else:
            return 2

    def suggest_learning_rate(self, epoch, current_iou, current_loss):
        """Dynamic learning rate suggestion based on performance"""
        if epoch < 50:
            # Early training - be conservative
            return 0.0001
        elif current_iou > 0.4:
            # Good performance - reduce LR for fine-tuning
            return 0.00005
        elif current_loss > 0.5:
            # High loss - increase LR to escape local minima
            return 0.0002
        else:
            # Default
            return 0.0001

    def suggest_weight_decay(self, current_iou, train_iou, val_iou):
        """Dynamic weight decay based on overfitting"""
        iou_gap = train_iou - val_iou

        if iou_gap > 0.1:  # Overfitting
            return 0.01  # Strong regularization
        elif iou_gap > 0.05:
            return 0.005  # Medium regularization
        else:
            return 0.001  # Light regularization

    def update_performance(self, epoch, iou, loss, params):
        """Update performance tracking"""
        if iou > self.best_iou:
            self.best_iou = iou
            self.best_params = params.copy()
            logger.info(f"🎯 New best IoU: {iou:.4f} with params: {params}")

        self.trial_history.append({
            'epoch': epoch,
            'iou': iou,
            'loss': loss,
            'params': params.copy()
        })


# ======================== ADVANCED CBAM FOR CRACK DETECTION ========================

class AdvancedCBAM(nn.Module):
    """RGB-OPTIMIZED CBAM for crack detection with color-aware attention"""

    def __init__(self, channels, reduction=8, input_channels=3):
        super(AdvancedCBAM, self).__init__()
        self.input_channels = input_channels
        self.is_rgb = (input_channels == 3)

        # RGB-Enhanced Channel Attention with color-aware processing
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        # RGB-specific: Add global context pooling for color information
        if self.is_rgb:
            self.global_context_pool = nn.AdaptiveAvgPool2d(4)  # 4x4 context for color patterns
            self.context_conv = nn.Sequential(
                nn.Conv2d(channels, channels // 4, 1, bias=False),
                nn.ReLU(inplace=True),
                nn.Conv2d(channels // 4, channels, 1, bias=False),  # Back to original channels
                nn.Sigmoid()
            )

        mid_channels = max(channels // reduction, 16)

        # Enhanced channel attention with RGB-specific processing
        if self.is_rgb:
            self.channel_att = nn.Sequential(
                nn.Conv2d(channels, mid_channels, 1, bias=False),
                nn.BatchNorm2d(mid_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(mid_channels, mid_channels, 1, bias=False),
                nn.BatchNorm2d(mid_channels),
                nn.ReLU(inplace=True),
                # RGB-specific: Add color-aware attention branch
                nn.Conv2d(mid_channels, mid_channels, 1, bias=False),
                nn.BatchNorm2d(mid_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(mid_channels, channels, 1, bias=False),
                nn.Sigmoid()
            )
        else:
            self.channel_att = nn.Sequential(
                nn.Conv2d(channels, mid_channels, 1, bias=False),
                nn.BatchNorm2d(mid_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(mid_channels, mid_channels, 1, bias=False),
                nn.BatchNorm2d(mid_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(mid_channels, channels, 1, bias=False),
                nn.Sigmoid()
            )

        # RGB-Enhanced Multi-scale Spatial Attention for crack connectivity
        spatial_input_channels = 3 if self.is_rgb else 2  # RGB uses avg+max+edge, IR uses avg+max

        self.spatial_att_3x3 = nn.Conv2d(spatial_input_channels, 1, 3, padding=1, bias=False)
        self.spatial_att_5x5 = nn.Conv2d(spatial_input_channels, 1, 5, padding=2, bias=False)
        self.spatial_att_7x7 = nn.Conv2d(spatial_input_channels, 1, 7, padding=3, bias=False)

        # Directional attention for long cracks
        self.horizontal_att = nn.Conv2d(spatial_input_channels, 1, kernel_size=(1, 7), padding=(0, 3), bias=False)
        self.vertical_att = nn.Conv2d(spatial_input_channels, 1, kernel_size=(7, 1), padding=(3, 0), bias=False)

        # RGB-specific: Add edge detection for crack boundaries
        if self.is_rgb:
            self.edge_detector = nn.Conv2d(channels, 1, 3, padding=1, bias=False)
            fusion_channels = 6  # 5 spatial + 1 edge
        else:
            fusion_channels = 5  # 5 spatial only

        # Enhanced fusion layer for multi-scale spatial attention
        self.spatial_fusion = nn.Sequential(
            nn.Conv2d(fusion_channels, 32, 3, padding=1, bias=False),  # More capacity for RGB
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 16, 3, padding=1, bias=False),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 1, 1, bias=False),
            nn.Sigmoid()
        )

        # Connectivity enhancement
        self.connectivity_conv = nn.Sequential(
            nn.Conv2d(channels, channels, 3, padding=1, groups=channels, bias=False),
            nn.Conv2d(channels, channels, 1, bias=False),
            nn.BatchNorm2d(channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        # RGB-Enhanced Channel Attention
        avg_out = self.channel_att(self.avg_pool(x))
        max_out = self.channel_att(self.max_pool(x))

        # RGB-specific: Add global context attention
        if self.is_rgb:
            context_out = self.context_conv(self.global_context_pool(x))
            context_out = F.adaptive_avg_pool2d(context_out, 1)  # Reduce to 1x1
            # Expand context to match channel dimensions
            context_out = F.interpolate(context_out, size=(1, 1), mode='nearest')
            context_out = context_out.expand_as(avg_out)  # Match dimensions
            channel_att = avg_out + max_out + context_out
        else:
            channel_att = avg_out + max_out

        x_channel = x * channel_att

        # RGB-Enhanced Multi-scale Spatial Attention
        avg_spatial = torch.mean(x_channel, dim=1, keepdim=True)
        max_spatial, _ = torch.max(x_channel, dim=1, keepdim=True)

        # RGB-specific: Add edge information for better crack detection
        if self.is_rgb:
            edge_spatial = self.edge_detector(x_channel)
            spatial_input = torch.cat([avg_spatial, max_spatial, edge_spatial], dim=1)
        else:
            spatial_input = torch.cat([avg_spatial, max_spatial], dim=1)

        # Different scale spatial attentions
        att_3x3 = self.spatial_att_3x3(spatial_input)
        att_5x5 = self.spatial_att_5x5(spatial_input)
        att_7x7 = self.spatial_att_7x7(spatial_input)

        # Directional attentions for long cracks
        att_h = self.horizontal_att(spatial_input)
        att_v = self.vertical_att(spatial_input)

        # Fuse all spatial attentions
        if self.is_rgb:
            spatial_combined = torch.cat([att_3x3, att_5x5, att_7x7, att_h, att_v, edge_spatial], dim=1)
        else:
            spatial_combined = torch.cat([att_3x3, att_5x5, att_7x7, att_h, att_v], dim=1)

        spatial_att = self.spatial_fusion(spatial_combined)
        x_spatial = x_channel * spatial_att

        # Connectivity enhancement for crack continuity
        x_connected = self.connectivity_conv(x_spatial)

        return x_connected + x  # Residual connection


class EfficientASPP(nn.Module):
    """Memory-efficient ASPP with depthwise separable convolutions"""

    def __init__(self, in_channels, out_channels):
        super(EfficientASPP, self).__init__()

        # Depthwise separable convolutions for efficiency
        self.conv1 = nn.Conv2d(in_channels, out_channels, 1, bias=False)

        # Depthwise + pointwise for dilated convs
        self.dw_conv2 = nn.Conv2d(in_channels, in_channels, 3, padding=6, dilation=6, groups=in_channels, bias=False)
        self.pw_conv2 = nn.Conv2d(in_channels, out_channels, 1, bias=False)

        self.dw_conv3 = nn.Conv2d(in_channels, in_channels, 3, padding=12, dilation=12, groups=in_channels, bias=False)
        self.pw_conv3 = nn.Conv2d(in_channels, out_channels, 1, bias=False)

        self.global_pool = nn.AdaptiveAvgPool2d(1)
        self.conv_global = nn.Conv2d(in_channels, out_channels, 1, bias=False)

        self.conv_out = nn.Conv2d(out_channels * 4, out_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        size = x.shape[-2:]

        x1 = self.conv1(x)
        x2 = self.pw_conv2(self.dw_conv2(x))
        x3 = self.pw_conv3(self.dw_conv3(x))
        x4 = F.interpolate(self.conv_global(self.global_pool(x)), size=size, mode='bilinear', align_corners=False)

        x = torch.cat([x1, x2, x3, x4], dim=1)
        x = self.conv_out(x)
        x = self.bn(x)
        x = self.relu(x)
        return x


class AttentionGate(nn.Module):
    """Attention gate for skip connections"""

    def __init__(self, F_g, F_l, F_int):
        super(AttentionGate, self).__init__()
        self.W_g = nn.Sequential(
            nn.Conv2d(F_g, F_int, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(F_int)
        )

        self.W_x = nn.Sequential(
            nn.Conv2d(F_l, F_int, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(F_int)
        )

        self.psi = nn.Sequential(
            nn.Conv2d(F_int, 1, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(1),
            nn.Sigmoid()
        )

        self.relu = nn.ReLU(inplace=True)

    def forward(self, g, x):
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.relu(g1 + x1)
        psi = self.psi(psi)
        return x * psi


# ======================== OPTIMIZED CSWIN-CBAM-UNET ========================

class OptimalCSWinUNet(nn.Module):
    """Advanced CSWin-CBAM-UNet with AdvancedCBAM for crack detection"""

    def __init__(self, num_classes=1, img_size=224, input_channels=1):  # FIXED: Binary segmentation
        super(OptimalCSWinUNet, self).__init__()
        self.num_classes = num_classes
        self.img_size = img_size
        self.input_channels = input_channels

        # RGB-optimized encoder that adapts to input channels
        self.encoder = nn.Sequential(
            nn.Conv2d(input_channels, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(),
            AdvancedCBAM(32, input_channels=input_channels)  # Pass input_channels for RGB optimization
        )

        # No fusion needed for single encoder

        # Enhanced encoder stages with RGB-optimized CBAM
        self.encoder1 = self._make_encoder_stage(32, 64, 1, input_channels)
        self.encoder2 = self._make_encoder_stage(64, 128, 1, input_channels)
        self.encoder3 = self._make_encoder_stage(128, 128, 1, input_channels)
        self.encoder4 = self._make_encoder_stage(128, 128, 1, input_channels)

        # Enhanced bottleneck with strong RGB-aware attention
        self.bottleneck = nn.Sequential(
            nn.Conv2d(128, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(),
            AdvancedCBAM(128, input_channels=input_channels),  # RGB-aware bottleneck
            nn.Dropout2d(0.3)
        )

        # Enhanced decoder stages with attention gates
        self.att_gate4 = AttentionGate(128, 128, 64)
        self.att_gate3 = AttentionGate(64, 128, 64)
        self.att_gate2 = AttentionGate(32, 64, 32)
        self.att_gate1 = AttentionGate(16, 32, 16)

        self.decoder4 = self._make_decoder_stage(128, 64, 64, input_channels)
        self.decoder3 = self._make_decoder_stage(64 + 128, 64, 32, input_channels)
        self.decoder2 = self._make_decoder_stage(32 + 64, 32, 16, input_channels)
        self.decoder1 = self._make_decoder_stage(16 + 32, 16, 8, input_channels)

        # Enhanced output head with final RGB-aware attention
        self.output_head = nn.Sequential(
            nn.Conv2d(8, 16, 3, padding=1),
            nn.BatchNorm2d(16),
            nn.ReLU(),
            AdvancedCBAM(16, input_channels=input_channels),  # RGB-aware final attention
            nn.Conv2d(16, 8, 3, padding=1),
            nn.BatchNorm2d(8),
            nn.ReLU(),
            nn.Dropout2d(0.3),
            nn.Conv2d(8, num_classes, 1)
        )

        # Initialize weights
        self._initialize_weights()

    def _make_encoder_stage(self, in_channels, out_channels, num_blocks, input_channels=3):
        """Enhanced encoder stage with RGB-optimized AdvancedCBAM"""
        layers = []
        layers.append(nn.Conv2d(in_channels, out_channels, 3, stride=2, padding=1))
        layers.append(nn.BatchNorm2d(out_channels))
        layers.append(nn.ReLU())
        layers.append(AdvancedCBAM(out_channels, input_channels=input_channels))  # RGB-aware CBAM

        for _ in range(num_blocks):
            layers.append(nn.Conv2d(out_channels, out_channels, 3, padding=1))
            layers.append(nn.BatchNorm2d(out_channels))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout2d(0.1))  # Light dropout

        return nn.Sequential(*layers)

    def _make_decoder_stage(self, in_channels, mid_channels, out_channels, input_channels=3):
        """Enhanced decoder stage with RGB-optimized AdvancedCBAM"""
        return nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, 3, padding=1),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(),
            AdvancedCBAM(mid_channels, input_channels=input_channels),  # RGB-aware CBAM
            nn.Conv2d(mid_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU()
        )

    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        """Enhanced forward pass with attention gates"""
        # Single encoder for input channels (RGB or IR)
        x0 = self.encoder(x)

        # Encoder stages with attention
        x1 = self.encoder1(x0)
        x2 = self.encoder2(x1)
        x3 = self.encoder3(x2)
        x4 = self.encoder4(x3)

        # Enhanced bottleneck
        bottleneck = self.bottleneck(x4)

        # Decoder stages with attention gates
        d4 = F.interpolate(bottleneck, size=x3.shape[-2:], mode='bilinear', align_corners=False)
        x3_att = self.att_gate4(d4, x3)  # Apply attention gate
        d4 = self.decoder4(d4)

        d3 = F.interpolate(d4, size=x2.shape[-2:], mode='bilinear', align_corners=False)
        x2_att = self.att_gate3(d3, x2)  # Apply attention gate
        d3 = self.decoder3(torch.cat([d3, x2_att], dim=1))

        d2 = F.interpolate(d3, size=x1.shape[-2:], mode='bilinear', align_corners=False)
        x1_att = self.att_gate2(d2, x1)  # Apply attention gate
        d2 = self.decoder2(torch.cat([d2, x1_att], dim=1))

        d1 = F.interpolate(d2, size=x0.shape[-2:], mode='bilinear', align_corners=False)
        x0_att = self.att_gate1(d1, x0)  # Apply attention gate
        d1 = self.decoder1(torch.cat([d1, x0_att], dim=1))

        # Enhanced output with final attention
        output = self.output_head(d1)
        output = F.interpolate(output, size=(self.img_size, self.img_size), mode='bilinear', align_corners=False)

        return output


# ======================== ADVANCED DATASET WITH STRONG AUGMENTATION ========================

class IRDataset(torch.utils.data.Dataset):
    def __init__(self, data_dir, split='train', img_size=224, dataset_type='ir'):
        self.data_dir = data_dir
        self.split = split
        self.img_size = img_size
        self.dataset_type = dataset_type

        # Choose image directory based on dataset type
        if dataset_type == 'rgb':
            self.image_dir = os.path.join(data_dir, '01-Visible images')
        else:  # ir
            self.image_dir = os.path.join(data_dir, '02-Infrared images')
        self.label_dir = os.path.join(data_dir, '04-Ground truth')

        # Find all valid image-label pairs
        self.samples = []
        for ext in ['*.png', '*.jpg', '*.jpeg']:
            for img_path in glob.glob(os.path.join(self.image_dir, ext)):
                img_name = os.path.splitext(os.path.basename(img_path))[0]
                # Try both .png and .jpg for labels
                label_path_png = os.path.join(self.label_dir, img_name + '.png')
                label_path_jpg = os.path.join(self.label_dir, img_name + '.jpg')

                if os.path.exists(label_path_png):
                    self.samples.append((img_path, label_path_png))
                elif os.path.exists(label_path_jpg):
                    self.samples.append((img_path, label_path_jpg))

        logger.info(f"Found {len(self.samples)} valid {dataset_type.upper()} samples for {split}")

        # RGB-OPTIMIZED augmentation for breakthrough performance
        if split == 'train':
            if dataset_type == 'rgb':
                self.transform = transforms.Compose([
                    transforms.ToPILImage(mode='RGB'),
                    transforms.Resize((img_size, img_size)),
                    transforms.RandomHorizontalFlip(p=0.7),  # Higher probability for RGB
                    transforms.RandomVerticalFlip(p=0.7),
                    transforms.RandomRotation((-60, 60)),  # More aggressive rotation for RGB
                    transforms.RandomAffine(
                        degrees=0,
                        translate=(0.2, 0.2),  # More translation for RGB robustness
                        scale=(0.7, 1.3),  # Wider scaling range for RGB
                        shear=(-12, 12)  # More aggressive shearing for RGB
                    ),
                    # RGB-SPECIFIC: Enhanced color augmentations
                    transforms.ColorJitter(
                        brightness=0.4,  # More brightness variation
                        contrast=0.4,  # More contrast variation
                        saturation=0.3,  # Saturation variation for RGB
                        hue=0.1  # Slight hue variation for RGB
                    ),
                    transforms.ToTensor()
                ])
            else:  # ir
                self.transform = transforms.Compose([
                    transforms.ToPILImage(mode='L'),
                    transforms.Resize((img_size, img_size)),
                    transforms.RandomHorizontalFlip(p=0.6),
                    transforms.RandomVerticalFlip(p=0.6),
                    transforms.RandomRotation((-45, 45)),  # More aggressive rotation
                    transforms.RandomAffine(
                        degrees=0,
                        translate=(0.15, 0.15),  # More translation for robustness
                        scale=(0.75, 1.25),  # More scaling variation
                        shear=(-8, 8)  # Moderate shearing
                    ),
                    transforms.ColorJitter(brightness=0.3, contrast=0.3),  # More variation
                    transforms.ToTensor()
                ])

            # Balanced augmentation for better generalization
            self.augment_factor = 4  # 4x augmentation for better validation performance

            self.mask_transform = transforms.Compose([
                transforms.ToPILImage(mode='L'),
                transforms.Resize((img_size, img_size), interpolation=transforms.InterpolationMode.NEAREST),
                transforms.ToTensor()
            ])
        else:
            if dataset_type == 'rgb':
                self.transform = transforms.Compose([
                    transforms.ToPILImage(mode='RGB'),
                    transforms.Resize((img_size, img_size)),
                    transforms.ToTensor()
                ])
            else:  # ir
                self.transform = transforms.Compose([
                    transforms.ToPILImage(mode='L'),
                    transforms.Resize((img_size, img_size)),
                    transforms.ToTensor()
                ])
            self.augment_factor = 1  # No augmentation for validation

        self.mask_transform = transforms.Compose([
            transforms.ToPILImage(mode='L'),
            transforms.Resize((img_size, img_size), interpolation=transforms.InterpolationMode.NEAREST),
            transforms.ToTensor()
        ])

    def __len__(self):
        return len(self.samples) * self.augment_factor

    def __getitem__(self, idx):
        # Map augmented index back to original sample
        original_idx = idx // self.augment_factor
        augment_idx = idx % self.augment_factor

        img_path, label_path = self.samples[original_idx]

        # Get original filename without extension
        filename = os.path.splitext(os.path.basename(img_path))[0]

        # Load image based on dataset type
        if self.dataset_type == 'rgb':
            image = cv2.imread(img_path, cv2.IMREAD_COLOR)
            if image is None:
                image = np.array(Image.open(img_path).convert('RGB'))
            else:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)  # Convert BGR to RGB
        else:  # ir
            image = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
            if image is None:
                image = np.array(Image.open(img_path).convert('L'))

        # Load label
        label = cv2.imread(label_path, cv2.IMREAD_GRAYSCALE)
        if label is None:
            label = np.array(Image.open(label_path).convert('L'))

        # Apply transforms
        if self.split == 'train':
            # Apply same random transforms to both image and mask
            seed = np.random.randint(2147483647)

            # Transform image
            np.random.seed(seed)
            torch.manual_seed(seed)
            image = self.transform(image)

            # Transform mask with same seed
            np.random.seed(seed)
            torch.manual_seed(seed)
            label = self.mask_transform(label)
        else:
            image = self.transform(image)
            label = self.mask_transform(label)

        # Convert to binary mask
        label = (label.squeeze() > 0.5).long()

        # Create 4-channel input (RGB + IR)
        # Return appropriate channels based on dataset type
        if self.dataset_type == 'rgb':
            # For RGB: return 3 channels
            if len(image.shape) == 3 and image.shape[0] == 3:
                input_tensor = image  # Already 3 channels
            else:
                # Single channel, repeat to create RGB
                if len(image.shape) == 2:
                    image = image.unsqueeze(0)
                input_tensor = image.repeat(3, 1, 1)
        else:  # ir
            # For IR: return 1 channel
            if len(image.shape) == 3 and image.shape[0] == 3:
                input_tensor = image[0:1]  # Use first channel only
            else:
                # Single channel IR
                if len(image.shape) == 2:
                    image = image.unsqueeze(0)
                input_tensor = image

        return {
            'image': input_tensor,
            'label': label,
            'case_name': os.path.splitext(os.path.basename(img_path))[0]
        }


# ======================== ADVANCED LOSS FUNCTIONS ========================

class AdaptiveLoss(nn.Module):
    """RGB-OPTIMIZED ADAPTIVE loss for breakthrough performance"""

    def __init__(self, num_classes=1, label_smoothing=0.05, dataset_type='rgb'):
        super(AdaptiveLoss, self).__init__()
        self.dataset_type = dataset_type
        self.num_classes = num_classes
        self.current_iou = 0.0

        # RGB-specific loss optimization
        if dataset_type == 'rgb':
            # Higher positive weight for RGB crack detection
            self.bce_loss = nn.BCEWithLogitsLoss(pos_weight=torch.tensor(25.0))  # Higher for RGB
            self.alpha = 0.5  # Higher alpha for RGB focal loss
            self.gamma = 3.5  # Higher gamma for RGB harder examples
        else:
            self.bce_loss = nn.BCEWithLogitsLoss(pos_weight=torch.tensor(20.0))
            self.alpha = 0.4
            self.gamma = 3.0

        # RGB-enhanced morphological kernels
        if dataset_type == 'rgb':
            self.register_buffer('connectivity_kernel', torch.ones(1, 1, 5, 5))  # Larger for RGB
            self.register_buffer('dilation_kernel', torch.ones(1, 1, 7, 7))  # Larger for RGB
            self.register_buffer('edge_kernel', torch.tensor([[[[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]]]]).float())
        else:
            self.register_buffer('connectivity_kernel', torch.ones(1, 1, 3, 3))
            self.register_buffer('dilation_kernel', torch.ones(1, 1, 5, 5))

    def lovasz_hinge_loss(self, pred, target):
        """Lovasz loss for better IoU optimization at high levels"""
        pred = F.softmax(pred, dim=1)[:, 1]  # Get foreground probabilities
        target = target.float()

        # Flatten
        pred_flat = pred.view(-1)
        target_flat = target.view(-1)

        # Lovasz extension of IoU loss
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum() - intersection
        iou = intersection / (union + 1e-6)

        # Lovasz hinge approximation
        errors = torch.abs(pred_flat - target_flat)
        errors_sorted, perm = torch.sort(errors, descending=True)
        gt_sorted = target_flat[perm]

        grad = self._lovasz_grad(gt_sorted)
        loss = torch.dot(errors_sorted, grad)

        return loss

    def _lovasz_grad(self, gt_sorted):
        """Compute gradient for Lovasz loss"""
        p = len(gt_sorted)
        gts = gt_sorted.sum()
        intersection = gts - gt_sorted.float().cumsum(0)
        union = gts + (1 - gt_sorted).float().cumsum(0)
        jaccard = 1. - intersection / union
        if p > 1:
            jaccard[1:p] = jaccard[1:p] - jaccard[0:-1]
        return jaccard

    def tversky_loss(self, pred, target, alpha=0.3, beta=0.7):
        """Tversky loss for handling class imbalance better"""
        smooth = 1e-6
        pred = F.softmax(pred, dim=1)
        target_one_hot = F.one_hot(target, self.num_classes).permute(0, 3, 1, 2).float()

        # Focus on foreground class
        pred_fg = pred[:, 1:2]
        target_fg = target_one_hot[:, 1:2]

        TP = (pred_fg * target_fg).sum(dim=(2, 3))
        FP = (pred_fg * (1 - target_fg)).sum(dim=(2, 3))
        FN = ((1 - pred_fg) * target_fg).sum(dim=(2, 3))

        tversky = (TP + smooth) / (TP + alpha * FP + beta * FN + smooth)
        return 1 - tversky.mean()

    def focal_loss(self, pred, target):
        """Enhanced focal loss with adaptive gamma"""
        ce_loss = F.cross_entropy(pred, target, reduction='none')
        pt = torch.exp(-ce_loss)

        # Adaptive gamma based on current IoU
        adaptive_gamma = self.gamma + (1.0 if self.current_iou > 0.6 else 0.0)
        focal_loss = self.alpha * (1 - pt) ** adaptive_gamma * ce_loss
        return focal_loss.mean()

    def connectivity_loss(self, pred, target):
        """Connectivity loss for crack continuity - optimized for spreading cracks"""
        pred_prob = F.softmax(pred, dim=1)[:, 1:2]  # Foreground probability
        target_float = target.float().unsqueeze(1)

        # Ensure kernels have same dtype and device as input
        connectivity_kernel = self.connectivity_kernel.to(pred_prob.dtype).to(pred_prob.device)
        dilation_kernel = self.dilation_kernel.to(pred_prob.dtype).to(pred_prob.device)

        # Morphological operations for connectivity
        pred_dilated = F.conv2d(pred_prob, dilation_kernel, padding=2)
        target_dilated = F.conv2d(target_float.to(pred_prob.dtype), dilation_kernel, padding=2)

        # Connectivity preservation
        connectivity_diff = torch.abs(pred_dilated - target_dilated)
        connectivity_loss = connectivity_diff.mean()

        # Penalize disconnected components
        pred_binary = (pred_prob > 0.5).float()
        target_binary = target_float.to(pred_prob.dtype)

        # Edge detection for crack boundaries
        pred_edges = F.conv2d(pred_binary, connectivity_kernel, padding=1) - pred_binary
        target_edges = F.conv2d(target_binary, connectivity_kernel, padding=1) - target_binary

        edge_loss = F.mse_loss(pred_edges, target_edges)

        return connectivity_loss + 0.5 * edge_loss

    def update_iou(self, current_iou):
        """Update current IoU for adaptive loss weighting"""
        self.current_iou = current_iou

    def forward(self, pred_logits, target):
        """RGB-OPTIMIZED binary segmentation loss"""
        # Ensure target is in correct format for binary segmentation
        if target.dim() == 3:  # [B, H, W]
            target = target.unsqueeze(1).float()  # [B, 1, H, W]
        else:
            target = target.float()

        # STABILIZED BCE loss for binary segmentation
        try:
            # Clamp logits to prevent overflow in sigmoid/BCE
            pred_logits_stable = torch.clamp(pred_logits, min=-10.0, max=10.0)
            bce_loss = self.bce_loss(pred_logits_stable, target)

            # Check for NaN in BCE loss
            if torch.isnan(bce_loss) or torch.isinf(bce_loss):
                bce_loss = torch.tensor(1.0, device=pred_logits.device, dtype=pred_logits.dtype)

        except Exception:
            bce_loss = torch.tensor(1.0, device=pred_logits.device, dtype=pred_logits.dtype)

        # STABILIZED Dice loss for better overlap
        try:
            pred_probs = torch.sigmoid(pred_logits_stable)
            # Add numerical stability
            pred_probs = torch.clamp(pred_probs, min=1e-7, max=1.0 - 1e-7)
            target_stable = torch.clamp(target, min=0.0, max=1.0)

            intersection = (pred_probs * target_stable).sum()
            union = pred_probs.sum() + target_stable.sum()
            dice_loss = 1 - (2 * intersection + 1e-6) / (union + 1e-6)

            # Check for NaN in Dice loss
            if torch.isnan(dice_loss) or torch.isinf(dice_loss):
                dice_loss = torch.tensor(0.5, device=pred_logits.device, dtype=pred_logits.dtype)

        except Exception:
            dice_loss = torch.tensor(0.5, device=pred_logits.device, dtype=pred_logits.dtype)

        # RGB-specific: STABILIZED edge-aware loss for better crack boundaries
        edge_loss = 0.0
        if self.dataset_type == 'rgb':
            try:
                # STABILIZED edge detection loss for RGB crack boundaries
                edge_kernel = self.edge_kernel.to(pred_probs.dtype).to(pred_probs.device)

                # Add numerical stability - clamp values to prevent overflow
                pred_probs_stable = torch.clamp(pred_probs, min=1e-7, max=1.0 - 1e-7)
                target_stable = torch.clamp(target, min=0.0, max=1.0)

                pred_edges = F.conv2d(pred_probs_stable, edge_kernel, padding=1)
                target_edges = F.conv2d(target_stable, edge_kernel, padding=1)

                # Normalize edge responses to prevent large gradients
                pred_edges = torch.tanh(pred_edges * 0.1)  # Scale down and normalize
                target_edges = torch.tanh(target_edges * 0.1)

                edge_loss = F.mse_loss(pred_edges, target_edges)

                # Additional stability check
                if torch.isnan(edge_loss) or torch.isinf(edge_loss):
                    edge_loss = 0.0

            except Exception as e:
                # Fallback: disable edge loss if any issues
                edge_loss = 0.0

        # STABILIZED RGB-optimized loss combination
        try:
            if self.dataset_type == 'rgb':
                if self.current_iou < 0.3:
                    # Early stage: Focus on BCE + dice for RGB (reduce edge weight for stability)
                    total_loss = 0.8 * bce_loss + 0.15 * dice_loss + 0.05 * edge_loss
                elif self.current_iou < 0.6:
                    # Mid stage: Balance all components for RGB
                    total_loss = 0.6 * bce_loss + 0.35 * dice_loss + 0.05 * edge_loss
                else:
                    # High IoU: Focus on Dice for RGB fine-tuning
                    total_loss = 0.4 * bce_loss + 0.55 * dice_loss + 0.05 * edge_loss
            else:
                # Original IR loss combination
                if self.current_iou < 0.3:
                    total_loss = 0.8 * bce_loss + 0.2 * dice_loss
                elif self.current_iou < 0.6:
                    total_loss = 0.6 * bce_loss + 0.4 * dice_loss
                else:
                    total_loss = 0.4 * bce_loss + 0.6 * dice_loss

            # Final stability check
            if torch.isnan(total_loss) or torch.isinf(total_loss):
                total_loss = bce_loss  # Fallback to simple BCE loss

            return total_loss

        except Exception:
            # Ultimate fallback
            return bce_loss


def calculate_metrics(pred_logits, target, threshold=0.5):
    """FIXED: Binary segmentation metrics calculation"""
    # Apply sigmoid to get probabilities
    pred_probs = torch.sigmoid(pred_logits)

    # Apply threshold to get binary predictions
    pred_binary = (pred_probs > threshold).float()

    # Ensure target is in correct format
    if target.dim() == 3:  # [B, H, W]
        target = target.unsqueeze(1)  # [B, 1, H, W]

    target_binary = (target > 0.5).float()

    # Calculate IoU for each sample in batch
    batch_size = pred_binary.size(0)
    ious = []
    dices = []

    for i in range(batch_size):
        pred_i = pred_binary[i].flatten()
        target_i = target_binary[i].flatten()

        # IoU calculation
        intersection = (pred_i * target_i).sum()
        union = pred_i.sum() + target_i.sum() - intersection

        if union == 0:
            iou = 1.0 if intersection == 0 else 0.0
        else:
            iou = (intersection / (union + 1e-8)).item()

        # Dice calculation
        dice_denom = pred_i.sum() + target_i.sum()
        if dice_denom == 0:
            dice = 1.0 if intersection == 0 else 0.0
        else:
            dice = (2 * intersection / (dice_denom + 1e-8)).item()

        ious.append(iou)
        dices.append(dice)

    # Calculate accuracy
    correct = (pred_binary == target_binary).float()
    accuracy = correct.mean().item()

    return {
        'iou': np.mean(ious),
        'dice': np.mean(dices),
        'accuracy': accuracy
    }


# ======================== MODEL EMA ========================

class ModelEMA:
    """Model Exponential Moving Average for better generalization"""

    def __init__(self, model, decay=0.9999):
        self.model = model
        self.decay = decay
        self.shadow = {}
        self.backup = {}

        # Initialize shadow weights
        for name, param in model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = param.data.clone()

    def update(self, model):
        for name, param in model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                new_average = (1.0 - self.decay) * param.data + self.decay * self.shadow[name]
                self.shadow[name] = new_average.clone()

    def apply_shadow(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.shadow
                self.backup[name] = param.data
                param.data = self.shadow[name]

    def restore(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                assert name in self.backup
                param.data = self.backup[name]
        self.backup = {}


# ======================== TRAINING PIPELINE ========================

def train_cswin_cbam_unet(data_dir='./data-if', epochs=150, batch_size=None, lr=None, target_iou=0.7,
                          dataset_type='rgb'):  # RGB-OPTIMIZED
    """RGB-OPTIMIZED training pipeline for breakthrough performance"""

    # DEBUG: Print received epochs
    print(f"DEBUG: Function received epochs = {epochs}")

    # RGB-specific learning rate optimization - ULTRA STABLE VERSION
    if lr is None:
        lr = 0.0015 if dataset_type == 'rgb' else 0.001  # Conservative LR for RGB numerical stability

    # Optimize GPU performance first
    optimize_gpu_performance()

    # Create checkpoint directory
    os.makedirs(f'checkpoints_cswin_cbam_unet_{dataset_type}', exist_ok=True)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024 ** 3
        logger.info(f"GPU: {gpu_name}")
        logger.info(f"CUDA Memory: {gpu_memory:.1f} GB")

    # Initialize hyperparameter optimizer
    hp_optimizer = HyperparameterOptimizer()

    # Auto-suggest batch size if not provided
    if batch_size is None:
        batch_size = hp_optimizer.suggest_batch_size(gpu_memory)
        logger.info(f"🎯 Auto-suggested batch size: {batch_size}")

    # Use gradient accumulation for larger effective batch size
    gradient_accumulation_steps = max(1, 8 // batch_size)
    effective_batch_size = batch_size * gradient_accumulation_steps
    logger.info(f"🔄 Gradient accumulation steps: {gradient_accumulation_steps}")
    logger.info(f"📊 Effective batch size: {effective_batch_size}")

    # Create dataset based on dataset_type
    full_dataset = IRDataset(data_dir, split='train', dataset_type=dataset_type)

    # STRATIFIED split to ensure similar foreground distribution in train/val
    # Calculate foreground ratios for stratification
    fg_ratios = []
    for i in range(len(full_dataset)):
        sample = full_dataset[i]
        label = sample['label']
        fg_ratio = (label == 1).float().mean().item()
        fg_ratios.append(fg_ratio)

    # Create stratified indices
    import numpy as np
    fg_ratios = np.array(fg_ratios)
    # Split into high/low foreground groups
    high_fg_indices = np.where(fg_ratios > 0.05)[0]  # >5% foreground
    low_fg_indices = np.where(fg_ratios <= 0.05)[0]  # <=5% foreground

    # Stratified split
    np.random.seed(42)  # For reproducibility
    np.random.shuffle(high_fg_indices)
    np.random.shuffle(low_fg_indices)

    train_high = high_fg_indices[:int(0.8 * len(high_fg_indices))]
    val_high = high_fg_indices[int(0.8 * len(high_fg_indices)):]
    train_low = low_fg_indices[:int(0.8 * len(low_fg_indices))]
    val_low = low_fg_indices[int(0.8 * len(low_fg_indices)):]

    train_indices = np.concatenate([train_high, train_low])
    val_indices = np.concatenate([val_high, val_low])

    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)
    val_dataset = torch.utils.data.Subset(full_dataset, val_indices)

    logger.info(f"Stratified split: Train={len(train_dataset)}, Val={len(val_dataset)}")
    logger.info(f"High FG samples - Train: {len(train_high)}, Val: {len(val_high)}")
    logger.info(f"Low FG samples - Train: {len(train_low)}, Val: {len(val_low)}")

    # Update val_dataset transform
    val_dataset.dataset.split = 'val'
    val_transform_dataset = IRDataset(data_dir, split='val', dataset_type=dataset_type)
    val_dataset.dataset.transform = val_transform_dataset.transform

    # Create data loaders with maximum performance optimization
    num_workers = min(8, os.cpu_count())  # Use more workers for better performance
    logger.info(f"🚀 Using {num_workers} workers for data loading")

    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        persistent_workers=True,
        prefetch_factor=4,  # Increased prefetch for better GPU utilization
        drop_last=True
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        persistent_workers=True,
        prefetch_factor=4,
        drop_last=False
    )

    # Create RGB-optimized model with appropriate input channels
    input_channels = 3 if dataset_type == 'rgb' else 1
    model = OptimalCSWinUNet(num_classes=1, input_channels=input_channels).to(device)

    # RGB-OPTIMIZED loss function for breakthrough performance
    criterion = AdaptiveLoss(num_classes=1, label_smoothing=0.05, dataset_type=dataset_type)

    # RGB-specific regularization optimization
    if dataset_type == 'rgb':
        initial_weight_decay = 0.03  # Lighter weight decay for RGB complexity
        betas = (0.9, 0.98)  # Different momentum for RGB
    else:
        initial_weight_decay = 0.05
        betas = (0.9, 0.999)

    optimizer = optim.AdamW(
        model.parameters(),
        lr=lr,
        weight_decay=initial_weight_decay,
        eps=1e-8,
        betas=betas
    )

    # RGB-OPTIMIZED learning rate scheduler with dataset-specific parameters
    scheduler = AdaptiveLRScheduler(optimizer, total_epochs=epochs, max_lr=lr, dataset_type=dataset_type)

    # Disable mixed precision to avoid dtype issues
    # scaler = GradScaler('cuda' if torch.cuda.is_available() else 'cpu')

    # FIXED: Aggressive early stopping to prevent overfitting
    best_iou = 0.0
    train_losses, val_losses, val_ious = [], [], []
    train_ious = []
    patience = 15  # FIXED: Much shorter patience to prevent overfitting
    patience_counter = 0

    logger.info("=" * 80)
    logger.info(f"🔥 RGB-OPTIMIZED CSWin-CBAM-UNet for {dataset_type.upper()} Dataset")
    logger.info("=" * 80)
    logger.info(f"🎯 Target VALIDATION IoU: {target_iou} (during training)")
    logger.info(f"📊 Total epochs: {epochs} (RGB-OPTIMIZED)")
    logger.info(f"📦 Batch size: {batch_size} (effective: {effective_batch_size})")
    logger.info(f"🚀 Learning rate: {lr} (RGB-OPTIMIZED: {'HIGH' if dataset_type == 'rgb' else 'STANDARD'})")
    logger.info(f"🔥 Loss: RGB-ENHANCED with edge-aware components")
    logger.info(f"🎨 CBAM: RGB-aware attention with color processing")
    logger.info(f"🌈 Augmentation: RGB-specific color space transforms")
    logger.info(f"⏰ Early stopping patience: {patience}")
    logger.info(f"🔄 Gradient accumulation steps: {gradient_accumulation_steps}")
    logger.info("=" * 80)

    for epoch in range(epochs):
        # Training
        model.train()
        train_loss = 0.0
        train_metrics = {'iou': 0.0, 'dice': 0.0, 'accuracy': 0.0}

        for batch_idx, batch in enumerate(tqdm(train_loader, desc=f"Epoch {epoch + 1}/{epochs}")):
            images = batch['image'].to(device, non_blocking=True)
            labels = batch['label'].to(device, non_blocking=True)

            # DISABLE MIXED PRECISION for numerical stability (RGB optimization)
            # with autocast(device_type='cuda' if torch.cuda.is_available() else 'cpu'):
            outputs = model(images)
            loss = criterion(outputs, labels)
            # Scale loss for gradient accumulation
            loss = loss / gradient_accumulation_steps

            # NUMERICAL STABILITY: Check for NaN loss before backward pass
            if torch.isnan(loss) or torch.isinf(loss):
                logger.warning(f"⚠️  NaN/Inf loss detected at batch {batch_idx}, skipping backward pass")
                continue

            loss.backward()

            # Gradient accumulation with GRADIENT CLIPPING for stability
            if (batch_idx + 1) % gradient_accumulation_steps == 0:
                # CRITICAL: Add gradient clipping to prevent gradient explosion
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

                # Check for NaN gradients
                has_nan_grad = False
                for param in model.parameters():
                    if param.grad is not None and (torch.isnan(param.grad).any() or torch.isinf(param.grad).any()):
                        has_nan_grad = True
                        break

                if has_nan_grad:
                    logger.warning(f"⚠️  NaN/Inf gradients detected at batch {batch_idx}, skipping optimizer step")
                    optimizer.zero_grad()
                    continue

                optimizer.step()
                optimizer.zero_grad()

            train_loss += loss.item() * gradient_accumulation_steps

            # Calculate metrics
            with torch.no_grad():
                metrics = calculate_metrics(outputs, labels)
                for key in train_metrics:
                    train_metrics[key] += metrics[key]

        # Validation with ORIGINAL model (NO EMA)
        model.eval()
        # ema.apply_shadow()

        val_loss = 0.0
        val_metrics = {'iou': 0.0, 'dice': 0.0, 'accuracy': 0.0}

        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(device, non_blocking=True)
                labels = batch['label'].to(device, non_blocking=True)

                # DISABLE MIXED PRECISION for numerical stability (RGB optimization)
                # with autocast(device_type='cuda' if torch.cuda.is_available() else 'cpu'):
                outputs = model(images)
                loss = criterion(outputs, labels)

                val_loss += loss.item()

                metrics = calculate_metrics(outputs, labels)
                for key in val_metrics:
                    val_metrics[key] += metrics[key]

        # ema.restore()

        # Calculate averages
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)

        for key in train_metrics:
            train_metrics[key] /= len(train_loader)
            val_metrics[key] /= len(val_loader)

        current_lr = optimizer.param_groups[0]['lr']

        # Store metrics
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        train_ious.append(train_metrics['iou'])
        val_ious.append(val_metrics['iou'])

        # Update adaptive loss with current IoU
        criterion.update_iou(val_metrics['iou'])

        # Step the adaptive scheduler with IoU feedback
        current_lr = scheduler.step(epoch, val_metrics['iou'])

        # Calculate train/val gap for overfitting detection
        iou_gap = train_metrics['iou'] - val_metrics['iou']

        logger.info(f"Epoch {epoch + 1}: Train Loss={avg_train_loss:.4f}, Train IoU={train_metrics['iou']:.4f}, "
                    f"Val Loss={avg_val_loss:.4f}, Val IoU={val_metrics['iou']:.4f}, "
                    f"IoU Gap={iou_gap:.4f}, LR={current_lr:.6f}")

        # NUMERICAL STABILITY: Check for NaN values in metrics
        if torch.isnan(torch.tensor(avg_val_loss)) or torch.isnan(torch.tensor(val_metrics['iou'])):
            logger.error(f"🚨 NaN values detected at epoch {epoch + 1}!")
            logger.error(f"   Val Loss: {avg_val_loss}, Val IoU: {val_metrics['iou']}")
            logger.error("   Stopping training to prevent further instability")
            break

        # Save best model and early stopping logic
        if val_metrics['iou'] > best_iou:
            best_iou = val_metrics['iou']
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.scheduler.state_dict(),
                'epoch': epoch,
                'best_iou': best_iou
            }, 'best_cswin_cbam_unet_ir.pth')
            logger.info(f"🏆 New best IoU: {best_iou:.4f}")
            patience_counter = 0
        else:
            patience_counter += 1

        # DISABLED - NO EARLY STOPPING
        # if avg_val_loss < best_val_loss:
        #     best_val_loss = avg_val_loss
        #     no_improve_counter = 0
        # else:
        #     no_improve_counter += 1

        # DISABLED - NO EARLY STOPPING
        # if no_improve_counter >= 15 and iou_gap > 0.25:  # 25% gap for extreme imbalance
        #     logger.info(f"⚠️  Early stopping due to severe overfitting: IoU gap {iou_gap:.4f} > 0.25")
        #     logger.info(f"   Train IoU: {train_metrics['iou']:.4f}, Val IoU: {val_metrics['iou']:.4f}")
        #     break

        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.scheduler.state_dict(),
                'best_iou': best_iou,
                'train_losses': train_losses,
                'val_losses': val_losses,
                'train_ious': train_ious,
                'val_ious': val_ious
            }, f'checkpoints_cswin_cbam_unet_{dataset_type}/checkpoint_epoch_{epoch + 1:03d}_{dataset_type}.pth')

        # DISABLED - NO EARLY STOPPING
        # if val_metrics['iou'] >= target_iou:
        #     logger.info(f"🎉 Target IoU {target_iou} achieved! Best IoU: {best_iou:.4f}")
        #     logger.info(f"Early stopping at epoch {epoch+1}")
        #     break

        # if patience_counter >= patience:
        #     logger.info(f"Early stopping due to no improvement for {patience} epochs")
        #     break

    # Generate results
    logger.info("\n" + "=" * 60)
    logger.info("🎯 GENERATING COMPREHENSIVE RESULTS")
    logger.info("=" * 60)

    # Plot training curves
    plot_training_curves(train_losses, val_losses, train_ious, val_ious)

    # Generate predictions
    generate_predictions(model, val_loader, device, dataset_type)

    return best_iou


def plot_training_curves(train_losses, val_losses, train_ious, val_ious):
    """Plot comprehensive training curves"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    epochs = range(1, len(train_losses) + 1)

    # Loss curves
    ax1.plot(epochs, train_losses, label='Train Loss', linewidth=2)
    ax1.plot(epochs, val_losses, label='Val Loss', linewidth=2)
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title('Loss Curves')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # IoU curves
    ax2.plot(epochs, train_ious, label='Train IoU', linewidth=2)
    ax2.plot(epochs, val_ious, label='Val IoU', linewidth=2)
    ax2.axhline(y=0.9, color='red', linestyle='--', linewidth=2, label='Target IoU (0.9)')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('IoU')
    ax2.set_title('IoU Progress')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 1)

    # Loss difference
    loss_diff = [val - train for val, train in zip(val_losses, train_losses)]
    ax3.plot(epochs, loss_diff, color='red', linewidth=2)
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Val Loss - Train Loss')
    ax3.set_title('Overfitting Indicator')
    ax3.grid(True, alpha=0.3)

    # IoU improvement
    iou_improvement = [0] + [val_ious[i] - val_ious[i - 1] for i in range(1, len(val_ious))]
    ax4.plot(epochs, iou_improvement, color='green', linewidth=2)
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('IoU Improvement')
    ax4.set_title('IoU Improvement Rate')
    ax4.grid(True, alpha=0.3)

    plt.suptitle('CSWin-CBAM-UNet Training Analysis - IR Dataset', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('cswin_cbam_unet_training_curves.png', dpi=300, bbox_inches='tight')
    plt.close()

    logger.info("📈 Training curves saved as 'cswin_cbam_unet_training_curves.png'")


# ======================== MAIN EXECUTION ========================

if __name__ == "__main__":
    """RGB-OPTIMIZED CSWin-CBAM-UNet Training"""

    # RGB-specific optimized parameters - ULTRA STABLE VERSION
    RGB_OPTIMIZED_PARAMS = {
        'data_dir': './data-if',
        'epochs': 150,
        'batch_size': None,  # Auto-detect based on GPU
        'lr': 0.0015,  # Conservative learning rate for numerical stability
        'target_iou': 0.7,
        'dataset_type': 'rgb'
    }

    logger.info("🚀 Starting RGB-OPTIMIZED CSWin-CBAM-UNet Training")
    logger.info("=" * 60)
    logger.info("🎨 RGB-SPECIFIC OPTIMIZATIONS (ULTRA STABLE VERSION):")
    logger.info("   ✅ Conservative learning rate (0.0015) with extended warmup")
    logger.info("   ✅ RGB-aware CBAM attention with numerical stability")
    logger.info("   ✅ Enhanced color augmentations")
    logger.info("   ✅ Stabilized edge-aware loss function")
    logger.info("   ✅ Color-space processing")
    logger.info("   ✅ Gradient clipping (max_norm=1.0)")
    logger.info("   ✅ NaN detection and handling")
    logger.info("   ✅ Mixed precision disabled for stability")
    logger.info("=" * 60)

    try:
        best_iou = train_cswin_cbam_unet(**RGB_OPTIMIZED_PARAMS)

        logger.info("=" * 60)
        logger.info("🎉 RGB TRAINING COMPLETED!")
        logger.info(f"🏆 Best Validation IoU: {best_iou:.4f}")

        if best_iou >= 0.7:
            logger.info("✅ TARGET IoU ACHIEVED!")
        else:
            logger.info("⚠️  Target IoU not reached, but model optimized")

        logger.info("=" * 60)

    except Exception as e:
        logger.error(f"❌ Training failed: {str(e)}")
        raise


def generate_predictions(model, val_loader, device, dataset_type='ir'):
    """Generate predictions with ORIGINAL model (not EMA)"""
    model.eval()
    # DON'T use EMA for inference - use original model

    # Create checkpoint directory
    os.makedirs(f'checkpoints_cswin_cbam_unet_{dataset_type}', exist_ok=True)
    os.makedirs('predictions_ir_cswin_cbam', exist_ok=True)

    all_ious = []

    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm(val_loader, desc="Generating predictions")):
            if batch_idx >= 10:  # Limit to first 10 batches for demo
                break

            images = batch['image'].to(device)
            labels = batch['label'].to(device)
            case_names = batch['case_name']

            # Use original model, not EMA
            outputs = model(images)
            # FIXED: Binary segmentation - use sigmoid + threshold instead of argmax
            predictions = (torch.sigmoid(outputs) > 0.5).float()

            for i in range(images.size(0)):
                # Calculate IoU for this sample
                pred_i = predictions[i:i + 1]
                label_i = labels[i:i + 1]
                metrics = calculate_metrics(outputs[i:i + 1], label_i)
                iou = metrics['iou']
                all_ious.append(iou)

                # Convert to numpy for visualization
                img_ir = images[i, 0].cpu().numpy()  # First channel (IR)
                label_np = labels[i].cpu().numpy()
                pred_np = predictions[i, 0].cpu().numpy()  # FIXED: Remove extra dimension

                # Create visualization with original filename
                case_name = case_names[i] if isinstance(case_names, list) else case_names[i].item()
                fig, axes = plt.subplots(1, 4, figsize=(16, 4))

                # Use grayscale for IR images to preserve original appearance
                axes[0].imshow(img_ir, cmap='gray', vmin=0, vmax=1)
                axes[0].set_title('IR Image')
                axes[0].axis('off')

                axes[1].imshow(label_np, cmap='Blues')
                axes[1].set_title('Ground Truth')
                axes[1].axis('off')

                axes[2].imshow(pred_np.squeeze(), cmap='Reds')  # FIXED: Remove all extra dimensions
                axes[2].set_title('Prediction')
                axes[2].axis('off')

                # Overlay
                axes[3].imshow(img_ir, cmap='gray', alpha=0.7)
                axes[3].imshow(pred_np.squeeze(), cmap='Reds', alpha=0.5)  # FIXED: Remove all extra dimensions
                axes[3].set_title('Overlay')
                axes[3].axis('off')

                color = 'green' if iou > 0.1 else 'orange' if iou > 0.05 else 'red'
                plt.suptitle(f'{case_name}_predict - IoU: {iou:.4f}', fontsize=14, fontweight='bold', color=color)

                plt.tight_layout()
                plt.savefig(f'predictions_ir_cswin_cbam/{case_name}_predict.png',
                            dpi=150, bbox_inches='tight')
                plt.close()

    # Summary statistics
    logger.info(f"\n📊 Prediction Summary:")
    logger.info(f"Total samples: {len(all_ious)}")
    logger.info(f"Mean IoU: {np.mean(all_ious):.4f}")
    logger.info(f"Std IoU: {np.std(all_ious):.4f}")
    logger.info(f"Min IoU: {np.min(all_ious):.4f}")
    logger.info(f"Max IoU: {np.max(all_ious):.4f}")
    logger.info(f"Samples with IoU > 0.1: {sum(1 for iou in all_ious if iou > 0.1)}/{len(all_ious)}")
    logger.info(f"Samples with IoU > 0.05: {sum(1 for iou in all_ious if iou > 0.05)}/{len(all_ious)}")


# ======================== MAIN EXECUTION ========================


def main():
    """Main function with argument parsing"""
    import argparse

    parser = argparse.ArgumentParser(description='CSWin-CBAM-UNet Training')
    parser.add_argument('--dataset_type', type=str, choices=['ir', 'rgb'], default='ir',
                        help='Dataset type (ir or rgb)')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=8,
                        help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='Learning rate')
    parser.add_argument('--data_dir', type=str, default='./data-if',
                        help='Data directory')

    args = parser.parse_args()

    # Train with parsed arguments
    train_cswin_cbam_unet(
        data_dir=args.data_dir,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.learning_rate,
        target_iou=0.7,  # Target VALIDATION IoU 0.7 during training
        dataset_type=args.dataset_type
    )


if __name__ == "__main__":
    main()
