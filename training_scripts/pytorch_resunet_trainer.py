#!/usr/bin/env python3
"""
ResU-Net Training Script for Crack Segmentation
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
import argparse
import logging
from tqdm import tqdm
import json
from datetime import datetime


# ResU-Net Implementation
class ResidualBlock(nn.Module):
    def __init__(self, in_channels, out_channels, stride=1):
        super(ResidualBlock, self).__init__()
        self.conv1 = nn.Conv2d(in_channels, out_channels, 3, stride=stride, padding=1, bias=False)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.conv2 = nn.Conv2d(out_channels, out_channels, 3, padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(out_channels)

        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, 1, stride=stride, bias=False),
                nn.BatchNorm2d(out_channels)
            )

    def forward(self, x):
        out = F.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += self.shortcut(x)
        out = F.relu(out)
        return out


class ResUNet(nn.Module):
    def __init__(self, n_channels=3, n_classes=1):
        super(ResUNet, self).__init__()
        self.n_channels = n_channels
        self.n_classes = n_classes

        # Encoder
        self.inc = nn.Sequential(
            nn.Conv2d(n_channels, 64, 7, padding=3, bias=False),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )

        self.layer1 = self._make_layer(64, 64, 2, stride=1)
        self.layer2 = self._make_layer(64, 128, 2, stride=2)
        self.layer3 = self._make_layer(128, 256, 2, stride=2)
        self.layer4 = self._make_layer(256, 512, 2, stride=2)

        # Bridge
        self.bridge = self._make_layer(512, 1024, 2, stride=2)

        # Decoder
        self.up1 = nn.ConvTranspose2d(1024, 512, 2, stride=2)
        self.dec1 = self._make_layer(1024, 512, 2, stride=1)

        self.up2 = nn.ConvTranspose2d(512, 256, 2, stride=2)
        self.dec2 = self._make_layer(512, 256, 2, stride=1)

        self.up3 = nn.ConvTranspose2d(256, 128, 2, stride=2)
        self.dec3 = self._make_layer(256, 128, 2, stride=1)

        self.up4 = nn.ConvTranspose2d(128, 64, 2, stride=2)
        self.dec4 = self._make_layer(128, 64, 2, stride=1)

        self.outc = nn.Conv2d(64, n_classes, 1)

    def _make_layer(self, in_channels, out_channels, blocks, stride):
        layers = []
        layers.append(ResidualBlock(in_channels, out_channels, stride))
        for _ in range(1, blocks):
            layers.append(ResidualBlock(out_channels, out_channels))
        return nn.Sequential(*layers)

    def forward(self, x):
        # Encoder
        x1 = self.inc(x)
        x2 = self.layer1(x1)
        x3 = self.layer2(x2)
        x4 = self.layer3(x3)
        x5 = self.layer4(x4)

        # Bridge
        bridge = self.bridge(x5)

        # Decoder
        d1 = self.up1(bridge)
        d1 = torch.cat([x5, d1], dim=1)
        d1 = self.dec1(d1)

        d2 = self.up2(d1)
        d2 = torch.cat([x4, d2], dim=1)
        d2 = self.dec2(d2)

        d3 = self.up3(d2)
        d3 = torch.cat([x3, d3], dim=1)
        d3 = self.dec3(d3)

        d4 = self.up4(d3)
        d4 = torch.cat([x2, d4], dim=1)
        d4 = self.dec4(d4)

        logits = self.outc(d4)
        return logits


# Dataset class (same as U-Net)
class CrackDataset(Dataset):
    def __init__(self, data_dir, dataset_type='ir', split='train'):
        self.data_dir = data_dir
        self.dataset_type = dataset_type
        self.split = split

        # Load samples
        if dataset_type == 'ir':
            img_dir = os.path.join(data_dir, '02-Infrared images')
        else:
            img_dir = os.path.join(data_dir, '01-Visible images')

        label_dir = os.path.join(data_dir, '04-Ground truth')

        self.samples = []
        for img_name in os.listdir(img_dir):
            if img_name.endswith(('.png', '.jpg', '.jpeg')):
                img_path = os.path.join(img_dir, img_name)
                label_name = img_name.replace('.png', '.jpg').replace('.jpg', '.jpg')
                label_path = os.path.join(label_dir, label_name)
                if os.path.exists(label_path):
                    self.samples.append((img_path, label_path))

        # Split data
        np.random.seed(42)
        indices = np.random.permutation(len(self.samples))
        split_idx = int(0.8 * len(self.samples))

        if split == 'train':
            self.samples = [self.samples[i] for i in indices[:split_idx]]
        else:
            self.samples = [self.samples[i] for i in indices[split_idx:]]

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        img_path, label_path = self.samples[idx]

        # Load image
        if self.dataset_type == 'ir':
            image = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
            image = cv2.resize(image, (224, 224))
            image = image.astype(np.float32) / 255.0
            image = np.expand_dims(image, axis=0)  # Add channel dimension
        else:
            image = cv2.imread(img_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            image = cv2.resize(image, (224, 224))
            image = image.astype(np.float32) / 255.0
            image = np.transpose(image, (2, 0, 1))  # HWC to CHW

        # Load label
        label = cv2.imread(label_path, cv2.IMREAD_GRAYSCALE)
        label = cv2.resize(label, (224, 224))
        label = (label > 127).astype(np.float32)

        return torch.from_numpy(image), torch.from_numpy(label)


def calculate_metrics(pred_logits, target, threshold=0.5):
    """Calculate IoU, Dice, Precision, Recall"""
    pred_probs = torch.sigmoid(pred_logits)
    pred_binary = (pred_probs > threshold).float()

    # Flatten tensors
    pred_flat = pred_binary.view(-1)
    target_flat = target.view(-1)

    # Calculate metrics
    intersection = (pred_flat * target_flat).sum()
    union = pred_flat.sum() + target_flat.sum() - intersection

    iou = intersection / (union + 1e-8)
    dice = 2 * intersection / (pred_flat.sum() + target_flat.sum() + 1e-8)

    tp = intersection
    fp = pred_flat.sum() - intersection
    fn = target_flat.sum() - intersection

    precision = tp / (tp + fp + 1e-8)
    recall = tp / (tp + fn + 1e-8)

    return {
        'iou': iou.item(),
        'dice': dice.item(),
        'precision': precision.item(),
        'recall': recall.item()
    }


def train_resunet(data_dir, dataset_type='ir', epochs=100, batch_size=8, lr=0.001, output_dir='resunet_output'):
    """Train ResU-Net model"""

    # Setup logging
    os.makedirs(output_dir, exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(output_dir, 'training.log')),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger(__name__)

    # Device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Model
    n_channels = 1 if dataset_type == 'ir' else 3
    model = ResUNet(n_channels=n_channels, n_classes=1).to(device)

    # Dataset and DataLoader
    train_dataset = CrackDataset(data_dir, dataset_type, 'train')
    val_dataset = CrackDataset(data_dir, dataset_type, 'val')

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

    # Loss and optimizer
    criterion = nn.BCEWithLogitsLoss()
    optimizer = optim.Adam(model.parameters(), lr=lr)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)

    # Training history
    history = {'train_loss': [], 'val_loss': [], 'train_iou': [], 'val_iou': []}
    best_iou = 0.0

    logger.info(f"Starting training for {epochs} epochs...")

    for epoch in range(epochs):
        # Training
        model.train()
        train_loss = 0.0
        train_metrics = {'iou': 0.0, 'dice': 0.0, 'precision': 0.0, 'recall': 0.0}

        for images, labels in tqdm(train_loader, desc=f'Epoch {epoch + 1}/{epochs} [Train]'):
            images, labels = images.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs.squeeze(1), labels)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()

            # Calculate metrics
            batch_metrics = calculate_metrics(outputs.squeeze(1), labels)
            for key in train_metrics:
                train_metrics[key] += batch_metrics[key]

        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)

        # Validation
        model.eval()
        val_loss = 0.0
        val_metrics = {'iou': 0.0, 'dice': 0.0, 'precision': 0.0, 'recall': 0.0}

        with torch.no_grad():
            for images, labels in tqdm(val_loader, desc=f'Epoch {epoch + 1}/{epochs} [Val]'):
                images, labels = images.to(device), labels.to(device)

                outputs = model(images)
                loss = criterion(outputs.squeeze(1), labels)
                val_loss += loss.item()

                # Calculate metrics
                batch_metrics = calculate_metrics(outputs.squeeze(1), labels)
                for key in val_metrics:
                    val_metrics[key] += batch_metrics[key]

        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)

        # Update history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_iou'].append(train_metrics['iou'])
        history['val_iou'].append(val_metrics['iou'])

        # Log progress
        logger.info(f"Epoch {epoch + 1}: Train Loss={train_loss:.4f}, Train IoU={train_metrics['iou']:.4f}, "
                    f"Val Loss={val_loss:.4f}, Val IoU={val_metrics['iou']:.4f}")

        # Save best model
        if val_metrics['iou'] > best_iou:
            best_iou = val_metrics['iou']
            torch.save(model.state_dict(), os.path.join(output_dir, f'best_resunet_{dataset_type}.pth'))
            logger.info(f"New best IoU: {best_iou:.4f}")

        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save(model.state_dict(),
                       os.path.join(output_dir, f'checkpoint_epoch_{epoch + 1:03d}_{dataset_type}.pth'))

        scheduler.step()

    # Save final results
    with open(os.path.join(output_dir, f'training_history_{dataset_type}.json'), 'w') as f:
        json.dump(history, f, indent=2)

    logger.info(f"Training completed! Best IoU: {best_iou:.4f}")
    return best_iou


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Train ResU-Net for crack segmentation')
    parser.add_argument('--data_dir', type=str, default='./data-if', help='Data directory')
    parser.add_argument('--dataset_type', type=str, default='ir', choices=['ir', 'rgb'], help='Dataset type')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--output_dir', type=str, default='resunet_output', help='Output directory')

    args = parser.parse_args()

    train_resunet(
        data_dir=args.data_dir,
        dataset_type=args.dataset_type,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.learning_rate,
        output_dir=args.output_dir
    )
