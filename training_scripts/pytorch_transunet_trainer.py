#!/usr/bin/env python3
"""
TransU-Net Training Script for Crack Segmentation
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
import argparse
import logging
from tqdm import tqdm
import json
from datetime import datetime
import math


# Simple Vision Transformer components
class PatchEmbedding(nn.Module):
    def __init__(self, img_size=224, patch_size=16, in_channels=3, embed_dim=768):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.n_patches = (img_size // patch_size) ** 2

        self.proj = nn.Conv2d(in_channels, embed_dim, kernel_size=patch_size, stride=patch_size)

    def forward(self, x):
        x = self.proj(x)  # (B, embed_dim, H/patch_size, W/patch_size)
        x = x.flatten(2)  # (B, embed_dim, n_patches)
        x = x.transpose(1, 2)  # (B, n_patches, embed_dim)
        return x


class MultiHeadAttention(nn.Module):
    def __init__(self, embed_dim=768, num_heads=12):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads

        self.qkv = nn.Linear(embed_dim, embed_dim * 3)
        self.proj = nn.Linear(embed_dim, embed_dim)

    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, self.head_dim).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]

        attn = (q @ k.transpose(-2, -1)) * (self.head_dim ** -0.5)
        attn = attn.softmax(dim=-1)

        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        return x


class TransformerBlock(nn.Module):
    def __init__(self, embed_dim=768, num_heads=12, mlp_ratio=4.0):
        super().__init__()
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = MultiHeadAttention(embed_dim, num_heads)
        self.norm2 = nn.LayerNorm(embed_dim)

        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Linear(mlp_hidden_dim, embed_dim)
        )

    def forward(self, x):
        x = x + self.attn(self.norm1(x))
        x = x + self.mlp(self.norm2(x))
        return x


class TransUNet(nn.Module):
    def __init__(self, n_channels=3, n_classes=1, img_size=224, patch_size=8, embed_dim=768, depth=12, num_heads=12):
        super().__init__()
        self.n_channels = n_channels
        self.n_classes = n_classes
        self.img_size = img_size
        self.patch_size = patch_size
        self.embed_dim = embed_dim

        # CNN Encoder (first few layers)
        self.conv1 = nn.Sequential(
            nn.Conv2d(n_channels, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )
        self.pool1 = nn.MaxPool2d(2)

        self.conv2 = nn.Sequential(
            nn.Conv2d(64, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True)
        )
        self.pool2 = nn.MaxPool2d(2)

        self.conv3 = nn.Sequential(
            nn.Conv2d(128, 256, 3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True)
        )

        # Transformer Encoder
        self.patch_embed = PatchEmbedding(img_size // 4, patch_size, 256, embed_dim)
        self.pos_embed = nn.Parameter(torch.zeros(1, self.patch_embed.n_patches, embed_dim))
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(embed_dim, num_heads) for _ in range(depth // 2)  # Reduced depth
        ])

        # Decoder
        self.decoder_embed = nn.Linear(embed_dim, 256)

        self.up1 = nn.ConvTranspose2d(256, 128, 2, stride=2)
        self.dec1 = nn.Sequential(
            nn.Conv2d(256, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, 3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True)
        )

        self.up2 = nn.ConvTranspose2d(128, 64, 2, stride=2)
        self.dec2 = nn.Sequential(
            nn.Conv2d(128, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, 3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )

        self.up3 = nn.ConvTranspose2d(64, 32, 2, stride=2)
        self.dec3 = nn.Sequential(
            nn.Conv2d(32, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 32, 3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True)
        )

        self.final_conv = nn.Conv2d(32, n_classes, 1)

    def forward(self, x):
        # CNN Encoder
        x1 = self.conv1(x)  # 64 channels
        x1_pool = self.pool1(x1)  # 112x112

        x2 = self.conv2(x1_pool)  # 128 channels
        x2_pool = self.pool2(x2)  # 56x56

        x3 = self.conv3(x2_pool)  # 256 channels, 56x56

        # Transformer Encoder
        x_patch = self.patch_embed(x3)  # (B, n_patches, embed_dim)
        x_patch = x_patch + self.pos_embed

        for block in self.transformer_blocks:
            x_patch = block(x_patch)

        # Reshape back to spatial
        B, N, C = x_patch.shape
        H = W = int(math.sqrt(N))
        x_spatial = x_patch.transpose(1, 2).reshape(B, self.embed_dim, H, W)

        # Decoder
        x_dec = self.decoder_embed(x_patch).transpose(1, 2).reshape(B, 256, H, W)

        # Upsampling with interpolation to match skip connections
        d1 = self.up1(x_dec)  # 128 channels
        # Resize d1 to match x2 spatial dimensions
        d1 = F.interpolate(d1, size=(x2.shape[2], x2.shape[3]), mode='bilinear', align_corners=False)
        d1 = torch.cat([x2, d1], dim=1)
        d1 = self.dec1(d1)

        d2 = self.up2(d1)  # 64 channels
        # Resize d2 to match x1 spatial dimensions
        d2 = F.interpolate(d2, size=(x1.shape[2], x1.shape[3]), mode='bilinear', align_corners=False)
        d2 = torch.cat([x1, d2], dim=1)
        d2 = self.dec2(d2)

        d3 = self.up3(d2)  # 32 channels, 448x448
        # Resize to match input size
        d3 = F.interpolate(d3, size=(self.img_size, self.img_size), mode='bilinear', align_corners=False)
        d3 = self.dec3(d3)

        logits = self.final_conv(d3)
        return logits


# Dataset class (same as others)
class CrackDataset(Dataset):
    def __init__(self, data_dir, dataset_type='ir', split='train'):
        self.data_dir = data_dir
        self.dataset_type = dataset_type
        self.split = split

        # Load samples
        if dataset_type == 'ir':
            img_dir = os.path.join(data_dir, '02-Infrared images')
        else:
            img_dir = os.path.join(data_dir, '01-Visible images')

        label_dir = os.path.join(data_dir, '04-Ground truth')

        self.samples = []
        for img_name in os.listdir(img_dir):
            if img_name.endswith(('.png', '.jpg', '.jpeg')):
                img_path = os.path.join(img_dir, img_name)
                label_name = img_name.replace('.png', '.jpg').replace('.jpg', '.jpg')
                label_path = os.path.join(label_dir, label_name)
                if os.path.exists(label_path):
                    self.samples.append((img_path, label_path))

        # Split data
        np.random.seed(42)
        indices = np.random.permutation(len(self.samples))
        split_idx = int(0.8 * len(self.samples))

        if split == 'train':
            self.samples = [self.samples[i] for i in indices[:split_idx]]
        else:
            self.samples = [self.samples[i] for i in indices[split_idx:]]

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        img_path, label_path = self.samples[idx]

        # Load image
        if self.dataset_type == 'ir':
            image = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
            image = cv2.resize(image, (224, 224))
            image = image.astype(np.float32) / 255.0
            image = np.expand_dims(image, axis=0)  # Add channel dimension
        else:
            image = cv2.imread(img_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            image = cv2.resize(image, (224, 224))
            image = image.astype(np.float32) / 255.0
            image = np.transpose(image, (2, 0, 1))  # HWC to CHW

        # Load label
        label = cv2.imread(label_path, cv2.IMREAD_GRAYSCALE)
        label = cv2.resize(label, (224, 224))
        label = (label > 127).astype(np.float32)

        return torch.from_numpy(image), torch.from_numpy(label)


def calculate_metrics(pred_logits, target, threshold=0.5):
    """Calculate IoU, Dice, Precision, Recall"""
    pred_probs = torch.sigmoid(pred_logits)
    pred_binary = (pred_probs > threshold).float()

    # Flatten tensors
    pred_flat = pred_binary.view(-1)
    target_flat = target.view(-1)

    # Calculate metrics
    intersection = (pred_flat * target_flat).sum()
    union = pred_flat.sum() + target_flat.sum() - intersection

    iou = intersection / (union + 1e-8)
    dice = 2 * intersection / (pred_flat.sum() + target_flat.sum() + 1e-8)

    tp = intersection
    fp = pred_flat.sum() - intersection
    fn = target_flat.sum() - intersection

    precision = tp / (tp + fp + 1e-8)
    recall = tp / (tp + fn + 1e-8)

    return {
        'iou': iou.item(),
        'dice': dice.item(),
        'precision': precision.item(),
        'recall': recall.item()
    }


def train_transunet(data_dir, dataset_type='ir', epochs=100, batch_size=4, lr=0.001, output_dir='transunet_output'):
    """Train TransU-Net model"""

    # Setup logging
    os.makedirs(output_dir, exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(output_dir, 'training.log')),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger(__name__)

    # Device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Model (smaller for memory efficiency)
    n_channels = 1 if dataset_type == 'ir' else 3
    model = TransUNet(n_channels=n_channels, n_classes=1, embed_dim=384, depth=6, num_heads=6).to(device)

    # Dataset and DataLoader
    train_dataset = CrackDataset(data_dir, dataset_type, 'train')
    val_dataset = CrackDataset(data_dir, dataset_type, 'val')

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=2)

    # Loss and optimizer
    criterion = nn.BCEWithLogitsLoss()
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)

    # Training history
    history = {'train_loss': [], 'val_loss': [], 'train_iou': [], 'val_iou': []}
    best_iou = 0.0

    logger.info(f"Starting training for {epochs} epochs...")

    for epoch in range(epochs):
        # Training
        model.train()
        train_loss = 0.0
        train_metrics = {'iou': 0.0, 'dice': 0.0, 'precision': 0.0, 'recall': 0.0}

        for images, labels in tqdm(train_loader, desc=f'Epoch {epoch + 1}/{epochs} [Train]'):
            images, labels = images.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs.squeeze(1), labels)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()

            # Calculate metrics
            batch_metrics = calculate_metrics(outputs.squeeze(1), labels)
            for key in train_metrics:
                train_metrics[key] += batch_metrics[key]

        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)

        # Validation
        model.eval()
        val_loss = 0.0
        val_metrics = {'iou': 0.0, 'dice': 0.0, 'precision': 0.0, 'recall': 0.0}

        with torch.no_grad():
            for images, labels in tqdm(val_loader, desc=f'Epoch {epoch + 1}/{epochs} [Val]'):
                images, labels = images.to(device), labels.to(device)

                outputs = model(images)
                loss = criterion(outputs.squeeze(1), labels)
                val_loss += loss.item()

                # Calculate metrics
                batch_metrics = calculate_metrics(outputs.squeeze(1), labels)
                for key in val_metrics:
                    val_metrics[key] += batch_metrics[key]

        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)

        # Update history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_iou'].append(train_metrics['iou'])
        history['val_iou'].append(val_metrics['iou'])

        # Log progress
        logger.info(f"Epoch {epoch + 1}: Train Loss={train_loss:.4f}, Train IoU={train_metrics['iou']:.4f}, "
                    f"Val Loss={val_loss:.4f}, Val IoU={val_metrics['iou']:.4f}")

        # Save best model
        if val_metrics['iou'] > best_iou:
            best_iou = val_metrics['iou']
            torch.save(model.state_dict(), os.path.join(output_dir, f'best_transunet_{dataset_type}.pth'))
            logger.info(f"New best IoU: {best_iou:.4f}")

        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save(model.state_dict(),
                       os.path.join(output_dir, f'checkpoint_epoch_{epoch + 1:03d}_{dataset_type}.pth'))

        scheduler.step()

    # Save final results
    with open(os.path.join(output_dir, f'training_history_{dataset_type}.json'), 'w') as f:
        json.dump(history, f, indent=2)

    logger.info(f"Training completed! Best IoU: {best_iou:.4f}")
    return best_iou


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Train TransU-Net for crack segmentation')
    parser.add_argument('--data_dir', type=str, default='./data-if', help='Data directory')
    parser.add_argument('--dataset_type', type=str, default='ir', choices=['ir', 'rgb'], help='Dataset type')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--output_dir', type=str, default='transunet_output', help='Output directory')

    args = parser.parse_args()

    train_transunet(
        data_dir=args.data_dir,
        dataset_type=args.dataset_type,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.learning_rate,
        output_dir=args.output_dir
    )
