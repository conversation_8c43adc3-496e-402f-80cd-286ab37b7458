#!/usr/bin/env python3
"""
CSWin-UNet Training Script for Crack Segmentation
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
import argparse
import logging
from tqdm import tqdm
import json
from datetime import datetime
import math


# Simplified CSWin Transformer components
class CSWinBlock(nn.Module):
    def __init__(self, dim, num_heads=8, split_size=7):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.split_size = split_size

        self.norm1 = nn.LayerNorm(dim)
        self.attn = nn.MultiheadAttention(dim, num_heads, batch_first=True)
        self.norm2 = nn.LayerNorm(dim)

        self.mlp = nn.Sequential(
            nn.Linear(dim, dim * 4),
            nn.<PERSON><PERSON><PERSON>(),
            nn.Linear(dim * 4, dim)
        )

    def forward(self, x):
        B, H, W, C = x.shape

        # Reshape for attention
        x_flat = x.view(B, H * W, C)

        # Self-attention
        x_norm = self.norm1(x_flat)
        attn_out, _ = self.attn(x_norm, x_norm, x_norm)
        x_flat = x_flat + attn_out

        # MLP
        x_flat = x_flat + self.mlp(self.norm2(x_flat))

        # Reshape back
        x = x_flat.view(B, H, W, C)
        return x


class CSWinUNet(nn.Module):
    def __init__(self, n_channels=3, n_classes=1, embed_dims=[64, 128, 256, 512]):
        super().__init__()
        self.n_channels = n_channels
        self.n_classes = n_classes

        # Patch embedding layers
        self.patch_embed1 = nn.Conv2d(n_channels, embed_dims[0], 4, stride=4)
        self.patch_embed2 = nn.Conv2d(embed_dims[0], embed_dims[1], 2, stride=2)
        self.patch_embed3 = nn.Conv2d(embed_dims[1], embed_dims[2], 2, stride=2)
        self.patch_embed4 = nn.Conv2d(embed_dims[2], embed_dims[3], 2, stride=2)

        # CSWin blocks
        self.block1 = CSWinBlock(embed_dims[0], num_heads=2)
        self.block2 = CSWinBlock(embed_dims[1], num_heads=4)
        self.block3 = CSWinBlock(embed_dims[2], num_heads=8)
        self.block4 = CSWinBlock(embed_dims[3], num_heads=16)

        # Decoder
        self.up4 = nn.ConvTranspose2d(embed_dims[3], embed_dims[2], 2, stride=2)
        self.dec4 = nn.Sequential(
            nn.Conv2d(embed_dims[2] * 2, embed_dims[2], 3, padding=1),
            nn.BatchNorm2d(embed_dims[2]),
            nn.ReLU(inplace=True)
        )

        self.up3 = nn.ConvTranspose2d(embed_dims[2], embed_dims[1], 2, stride=2)
        self.dec3 = nn.Sequential(
            nn.Conv2d(embed_dims[1] * 2, embed_dims[1], 3, padding=1),
            nn.BatchNorm2d(embed_dims[1]),
            nn.ReLU(inplace=True)
        )

        self.up2 = nn.ConvTranspose2d(embed_dims[1], embed_dims[0], 2, stride=2)
        self.dec2 = nn.Sequential(
            nn.Conv2d(embed_dims[0] * 2, embed_dims[0], 3, padding=1),
            nn.BatchNorm2d(embed_dims[0]),
            nn.ReLU(inplace=True)
        )

        self.up1 = nn.ConvTranspose2d(embed_dims[0], 32, 4, stride=4)
        self.final_conv = nn.Conv2d(32, n_classes, 1)

    def forward(self, x):
        B, C, H, W = x.shape

        # Encoder
        x1 = self.patch_embed1(x)  # B, 64, H/4, W/4
        B, C, H1, W1 = x1.shape
        x1_block = self.block1(x1.permute(0, 2, 3, 1)).permute(0, 3, 1, 2)  # Apply CSWin block

        x2 = self.patch_embed2(x1_block)  # B, 128, H/8, W/8
        B, C, H2, W2 = x2.shape
        x2_block = self.block2(x2.permute(0, 2, 3, 1)).permute(0, 3, 1, 2)

        x3 = self.patch_embed3(x2_block)  # B, 256, H/16, W/16
        B, C, H3, W3 = x3.shape
        x3_block = self.block3(x3.permute(0, 2, 3, 1)).permute(0, 3, 1, 2)

        x4 = self.patch_embed4(x3_block)  # B, 512, H/32, W/32
        B, C, H4, W4 = x4.shape
        x4_block = self.block4(x4.permute(0, 2, 3, 1)).permute(0, 3, 1, 2)

        # Decoder
        d4 = self.up4(x4_block)  # B, 256, H/16, W/16
        d4 = torch.cat([x3_block, d4], dim=1)
        d4 = self.dec4(d4)

        d3 = self.up3(d4)  # B, 128, H/8, W/8
        d3 = torch.cat([x2_block, d3], dim=1)
        d3 = self.dec3(d3)

        d2 = self.up2(d3)  # B, 64, H/4, W/4
        d2 = torch.cat([x1_block, d2], dim=1)
        d2 = self.dec2(d2)

        d1 = self.up1(d2)  # B, 32, H, W
        logits = self.final_conv(d1)  # B, 1, H, W

        return logits


# Dataset class (same as others)
class CrackDataset(Dataset):
    def __init__(self, data_dir, dataset_type='ir', split='train'):
        self.data_dir = data_dir
        self.dataset_type = dataset_type
        self.split = split

        # Load samples
        if dataset_type == 'ir':
            img_dir = os.path.join(data_dir, '02-Infrared images')
        else:
            img_dir = os.path.join(data_dir, '01-Visible images')

        label_dir = os.path.join(data_dir, '04-Ground truth')

        self.samples = []
        for img_name in os.listdir(img_dir):
            if img_name.endswith(('.png', '.jpg', '.jpeg')):
                img_path = os.path.join(img_dir, img_name)
                label_name = img_name.replace('.png', '.jpg').replace('.jpg', '.jpg')
                label_path = os.path.join(label_dir, label_name)
                if os.path.exists(label_path):
                    self.samples.append((img_path, label_path))

        # Split data
        np.random.seed(42)
        indices = np.random.permutation(len(self.samples))
        split_idx = int(0.8 * len(self.samples))

        if split == 'train':
            self.samples = [self.samples[i] for i in indices[:split_idx]]
        else:
            self.samples = [self.samples[i] for i in indices[split_idx:]]

    def __len__(self):
        return len(self.samples)

    def __getitem__(self, idx):
        img_path, label_path = self.samples[idx]

        # Load image
        if self.dataset_type == 'ir':
            image = cv2.imread(img_path, cv2.IMREAD_GRAYSCALE)
            image = cv2.resize(image, (224, 224))
            image = image.astype(np.float32) / 255.0
            image = np.expand_dims(image, axis=0)  # Add channel dimension
        else:
            image = cv2.imread(img_path)
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            image = cv2.resize(image, (224, 224))
            image = image.astype(np.float32) / 255.0
            image = np.transpose(image, (2, 0, 1))  # HWC to CHW

        # Load label
        label = cv2.imread(label_path, cv2.IMREAD_GRAYSCALE)
        label = cv2.resize(label, (224, 224))
        label = (label > 127).astype(np.float32)

        return torch.from_numpy(image), torch.from_numpy(label)


def calculate_metrics(pred_logits, target, threshold=0.5):
    """Calculate IoU, Dice, Precision, Recall"""
    pred_probs = torch.sigmoid(pred_logits)
    pred_binary = (pred_probs > threshold).float()

    # Flatten tensors
    pred_flat = pred_binary.view(-1)
    target_flat = target.view(-1)

    # Calculate metrics
    intersection = (pred_flat * target_flat).sum()
    union = pred_flat.sum() + target_flat.sum() - intersection

    iou = intersection / (union + 1e-8)
    dice = 2 * intersection / (pred_flat.sum() + target_flat.sum() + 1e-8)

    tp = intersection
    fp = pred_flat.sum() - intersection
    fn = target_flat.sum() - intersection

    precision = tp / (tp + fp + 1e-8)
    recall = tp / (tp + fn + 1e-8)

    return {
        'iou': iou.item(),
        'dice': dice.item(),
        'precision': precision.item(),
        'recall': recall.item()
    }


def train_cswinunet(data_dir, dataset_type='ir', epochs=100, batch_size=8, lr=0.001, output_dir='cswinunet_output'):
    """Train CSWin-UNet model"""

    # Setup logging
    os.makedirs(output_dir, exist_ok=True)
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(output_dir, 'training.log')),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger(__name__)

    # Device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Model
    n_channels = 1 if dataset_type == 'ir' else 3
    model = CSWinUNet(n_channels=n_channels, n_classes=1).to(device)

    # Dataset and DataLoader
    train_dataset = CrackDataset(data_dir, dataset_type, 'train')
    val_dataset = CrackDataset(data_dir, dataset_type, 'val')

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)

    # Loss and optimizer
    criterion = nn.BCEWithLogitsLoss()
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)

    # Training history
    history = {'train_loss': [], 'val_loss': [], 'train_iou': [], 'val_iou': []}
    best_iou = 0.0

    logger.info(f"Starting training for {epochs} epochs...")

    for epoch in range(epochs):
        # Training
        model.train()
        train_loss = 0.0
        train_metrics = {'iou': 0.0, 'dice': 0.0, 'precision': 0.0, 'recall': 0.0}

        for images, labels in tqdm(train_loader, desc=f'Epoch {epoch + 1}/{epochs} [Train]'):
            images, labels = images.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs.squeeze(1), labels)
            loss.backward()
            optimizer.step()

            train_loss += loss.item()

            # Calculate metrics
            batch_metrics = calculate_metrics(outputs.squeeze(1), labels)
            for key in train_metrics:
                train_metrics[key] += batch_metrics[key]

        train_loss /= len(train_loader)
        for key in train_metrics:
            train_metrics[key] /= len(train_loader)

        # Validation
        model.eval()
        val_loss = 0.0
        val_metrics = {'iou': 0.0, 'dice': 0.0, 'precision': 0.0, 'recall': 0.0}

        with torch.no_grad():
            for images, labels in tqdm(val_loader, desc=f'Epoch {epoch + 1}/{epochs} [Val]'):
                images, labels = images.to(device), labels.to(device)

                outputs = model(images)
                loss = criterion(outputs.squeeze(1), labels)
                val_loss += loss.item()

                # Calculate metrics
                batch_metrics = calculate_metrics(outputs.squeeze(1), labels)
                for key in val_metrics:
                    val_metrics[key] += batch_metrics[key]

        val_loss /= len(val_loader)
        for key in val_metrics:
            val_metrics[key] /= len(val_loader)

        # Update history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_iou'].append(train_metrics['iou'])
        history['val_iou'].append(val_metrics['iou'])

        # Log progress
        logger.info(f"Epoch {epoch + 1}: Train Loss={train_loss:.4f}, Train IoU={train_metrics['iou']:.4f}, "
                    f"Val Loss={val_loss:.4f}, Val IoU={val_metrics['iou']:.4f}")

        # Save best model
        if val_metrics['iou'] > best_iou:
            best_iou = val_metrics['iou']
            torch.save(model.state_dict(), os.path.join(output_dir, f'best_cswinunet_{dataset_type}.pth'))
            logger.info(f"New best IoU: {best_iou:.4f}")

        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save(model.state_dict(),
                       os.path.join(output_dir, f'checkpoint_epoch_{epoch + 1:03d}_{dataset_type}.pth'))

        scheduler.step()

    # Save final results
    with open(os.path.join(output_dir, f'training_history_{dataset_type}.json'), 'w') as f:
        json.dump(history, f, indent=2)

    logger.info(f"Training completed! Best IoU: {best_iou:.4f}")
    return best_iou


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Train CSWin-UNet for crack segmentation')
    parser.add_argument('--data_dir', type=str, default='./data-if', help='Data directory')
    parser.add_argument('--dataset_type', type=str, default='ir', choices=['ir', 'rgb'], help='Dataset type')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--output_dir', type=str, default='cswinunet_output', help='Output directory')

    args = parser.parse_args()

    train_cswinunet(
        data_dir=args.data_dir,
        dataset_type=args.dataset_type,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.learning_rate,
        output_dir=args.output_dir
    )
