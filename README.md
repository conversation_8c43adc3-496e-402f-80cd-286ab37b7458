# CSWin-CBAM-UNet for Crack Segmentation

🚀 **Optimized crack segmentation using CSWin Transformer + CBAM attention + U-Net architecture**

**Target Performance**: 0.65+ IoU for Infrared, Highest for RGB

## 🎯 Project Overview

This repository implements a comprehensive crack segmentation solution with:

- **CSWin-CBAM-UNet**: Main optimized model combining CSWin Transformer, CBAM attention, and U-Net
- **Comparison Models**: U-Net, ResU-Net for benchmarking
- **Dual Image Support**: Both Visible (RGB) and Infrared (IR) images
- **Complete Pipeline**: Training, evaluation, deployment, and visualization

## 📊 Key Features

### 🏗️ Architecture
- **CSWin Transformer**: Cross-shaped window attention for efficient processing
- **CBAM Attention**: Channel and spatial attention mechanisms
- **U-Net Decoder**: Skip connections for precise localization
- **Mixed Precision**: Faster training with reduced memory usage

### 📈 Comprehensive Metrics
- **Accuracy, Precision, Recall, F1-Score, IoU**
- **Real-time training monitoring**
- **Automatic best model saving**
- **Training curve visualization**

### 🔧 Advanced Features
- **IR-specific preprocessing**: CLAHE enhancement, edge detection
- **Smart data augmentation**: Tailored for crack detection
- **Early stopping**: Target-based and patience-based
- **One-command deployment**: Simple inference pipeline

## 📁 Project Structure

### Core Training Files
- `optimal_cswin_trainer.py` - **Main training script** (supports both RGB and IR with target IoU)
- `run_optimal_training.py` - **Primary runner script** with multiple modes

### Optimization Files
- `optimize_hyperparams.py` - Hyperparameter optimization with Optuna
- `run_optimization.py` - Interactive optimization runner
- `run_full_optimization.py` - Full optimization for both datasets
- `demo_optimization.py` - Quick optimization demo

### Configuration & Networks
- `config.py` - Configuration management
- `configs/cswin_tiny_224_lite.yaml` - Model configuration
- `networks/` - Network architectures (CSWin-UNet, Vision Transformer)
- `datasets/` - Dataset loading utilities

### Utilities
- `utils.py` - Loss functions and metrics
- `trainer_optuna.py` - Optuna-based trainer

## 🚀 Quick Start

### 1. Installation
```bash
pip install -r requirements.txt
```

### 2. Dataset Setup
Place your dataset in the following structure:
```
data-if/
├── 01-Visible images/
├── 02-Infrared images/
└── 04-Ground truth/
```

### 3. Quick Demo Training
```bash
python run_optimal_training.py --demo
```

### 4. Full Training (RGB)
```bash
python optimal_cswin_trainer.py --image_type rgb --epochs 50 --target_iou 0.85
```

### 5. Full Training (IR)
```bash
python optimal_cswin_trainer.py --image_type ir --epochs 50 --target_iou 0.85
```

### 6. Comparison Training (Both RGB and IR)
```bash
python optimal_cswin_trainer.py --compare
```

## 📊 Expected Results

- **Target IoU**: > 0.85 for both RGB and IR datasets
- **RGB Dataset**: Typically achieves 0.80-0.90 IoU
- **IR Dataset**: Typically achieves 0.75-0.85 IoU
- **Training Time**: 1-3 hours depending on epochs and hardware

## 📈 Output Files

After training, you'll find:
```
best_optimal_cswin_rgb.pth          # Best model weights
predictions_rgb/                    # Prediction visualizations
├── batch_0_sample_0.png           # Input | Ground Truth | Prediction
├── batch_0_sample_1.png
└── ...
training_curves_rgb.png             # Loss and IoU curves
comparison_results.png               # RGB vs IR comparison (if --compare used)
```

## 🔧 Advanced Usage

### Hyperparameter Optimization
```bash
# Quick demo optimization
python demo_optimization.py

# Full optimization for RGB
python optimize_hyperparams.py --dataset DataIF_RGB --n_trials 20

# Full optimization for both datasets
python run_full_optimization.py
```

### Interactive Mode
```bash
python run_optimization.py
```

## 📚 Documentation

- `README_OPTIMAL_CSWIN.md` - Detailed documentation for optimal training
- `README_OPTIMIZATION.md` - Hyperparameter optimization guide
- `Optimal_CSWin_UNET.md` - Architecture and implementation details

## References
* [Swin-Unet](https://github.com/HuCaoFighting/Swin-Unet)
* [CSWin-Transformer](https://github.com/microsoft/CSWin-Transformer)

## Citation

```bibtex
@article{liu2025cswin,
  title={CSWin-UNet: Transformer UNet with cross-shaped windows for medical image segmentation},
  author={Liu, Xiao and Gao, Peng and Yu, Tao and Wang, Fei and Yuan, Ru-Yue},
  journal={Information Fusion},
  volume={113},
  pages={102634},
  year={2025},
  publisher={Elsevier}
}
```
