# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt

# Training outputs
output_*/
predictions_*/
demo_output/
optuna_output_*/

# Logs and metrics
*.log
*.txt
!requirements.txt
!README*.txt

# Training curves and plots
*.png
*.jpg
*.jpeg
!data-if/**/*.png
!data-if/**/*.jpg
!data-if/**/*.jpeg

# Pickle files
*.pkl
*.pickle

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Data folders (keep structure but ignore large files)
data-if/**/*.png
data-if/**/*.jpg
data-if/**/*.jpeg
!data-if/README.md

# Model checkpoints and weights
*.ckpt
*.h5
*.hdf5

# Temporary files
*.tmp
*.temp
